<!DOCTYPE html>
<html>
<head>
    <title>Solana钱包连接演示</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #1a1a1a;
            color: white;
            max-width: 800px;
            margin: 0 auto;
        }
        .demo-section {
            margin: 20px 0;
            padding: 20px;
            background: #2a2a2a;
            border-radius: 10px;
            border: 1px solid #444;
        }
        .wallet-button {
            background: linear-gradient(135deg, #9945FF, #14F195);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: all 0.3s ease;
        }
        .wallet-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(153, 69, 255, 0.3);
        }
        .wallet-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-family: monospace;
        }
        .success { background: rgba(20, 241, 149, 0.1); border: 1px solid #14F195; }
        .error { background: rgba(255, 69, 69, 0.1); border: 1px solid #ff4545; }
        .info { background: rgba(153, 69, 255, 0.1); border: 1px solid #9945FF; }
        pre {
            background: #333;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <h1>🔗 Solana钱包连接演示</h1>
    
    <div class="demo-section">
        <h2>📋 重要说明</h2>
        <div class="status info">
            <strong>关于@solana/wallet-adapter：</strong><br>
            官方的@solana/wallet-adapter主要为React应用设计，不直接支持vanilla JavaScript。<br>
            对于静态HTML网站，我们使用钱包的原生API（如window.solana）。
        </div>
    </div>

    <div class="demo-section">
        <h2>🔍 钱包检测</h2>
        <button class="wallet-button" onclick="detectWallets()">检测已安装的钱包</button>
        <div id="detection-results"></div>
    </div>

    <div class="demo-section">
        <h2>🔌 钱包连接</h2>
        <button class="wallet-button" onclick="connectPhantom()" id="phantom-btn">连接 Phantom</button>
        <button class="wallet-button" onclick="connectSolflare()" id="solflare-btn">连接 Solflare</button>
        <button class="wallet-button" onclick="disconnect()" id="disconnect-btn" disabled>断开连接</button>
        <div id="connection-status"></div>
    </div>

    <div class="demo-section">
        <h2>📝 代码示例</h2>
        <h3>正确的Phantom连接方法：</h3>
        <pre><code>// 检测Phantom钱包
if (window.solana && window.solana.isPhantom) {
    console.log('Phantom钱包已安装');
    
    // 连接钱包
    const response = await window.solana.connect();
    console.log('连接成功:', response.publicKey.toString());
} else {
    console.log('请安装Phantom钱包');
}</code></pre>
    </div>

    <script>
        let currentWallet = null;
        let currentPublicKey = null;

        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('connection-status');
            statusDiv.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        function detectWallets() {
            const resultsDiv = document.getElementById('detection-results');
            const wallets = [
                { name: 'Phantom', check: () => window.solana?.isPhantom, obj: 'window.solana' },
                { name: 'Solflare', check: () => window.solflare?.isSolflare, obj: 'window.solflare' },
                { name: 'Slope', check: () => window.slope, obj: 'window.slope' },
                { name: 'Backpack', check: () => window.backpack, obj: 'window.backpack' },
                { name: 'Coin98', check: () => window.coin98?.solana, obj: 'window.coin98.solana' }
            ];

            let html = '<h3>检测结果：</h3>';
            let foundAny = false;

            wallets.forEach(wallet => {
                const isInstalled = wallet.check();
                if (isInstalled) foundAny = true;
                html += `<div class="status ${isInstalled ? 'success' : 'error'}">
                    ${wallet.name}: ${isInstalled ? '✅ 已安装' : '❌ 未安装'} (${wallet.obj})
                </div>`;
            });

            if (!foundAny) {
                html += '<div class="status error">未检测到任何Solana钱包。请安装Phantom、Solflare等钱包扩展。</div>';
            }

            resultsDiv.innerHTML = html;
        }

        async function connectPhantom() {
            try {
                if (!window.solana || !window.solana.isPhantom) {
                    updateStatus('Phantom钱包未安装。<a href="https://phantom.app" target="_blank">点击安装</a>', 'error');
                    return;
                }

                updateStatus('正在连接Phantom钱包...', 'info');
                const response = await window.solana.connect();
                
                currentWallet = window.solana;
                currentPublicKey = response.publicKey.toString();
                
                updateStatus(`✅ Phantom连接成功！<br>地址: ${currentPublicKey}`, 'success');
                
                document.getElementById('disconnect-btn').disabled = false;
                document.getElementById('phantom-btn').textContent = '已连接 Phantom';
                
            } catch (error) {
                updateStatus(`❌ 连接失败: ${error.message}`, 'error');
            }
        }

        async function connectSolflare() {
            try {
                if (!window.solflare || !window.solflare.isSolflare) {
                    updateStatus('Solflare钱包未安装。<a href="https://solflare.com" target="_blank">点击安装</a>', 'error');
                    return;
                }

                updateStatus('正在连接Solflare钱包...', 'info');
                const response = await window.solflare.connect();
                
                currentWallet = window.solflare;
                currentPublicKey = response.publicKey.toString();
                
                updateStatus(`✅ Solflare连接成功！<br>地址: ${currentPublicKey}`, 'success');
                
                document.getElementById('disconnect-btn').disabled = false;
                document.getElementById('solflare-btn').textContent = '已连接 Solflare';
                
            } catch (error) {
                updateStatus(`❌ 连接失败: ${error.message}`, 'error');
            }
        }

        async function disconnect() {
            try {
                if (currentWallet && currentWallet.disconnect) {
                    await currentWallet.disconnect();
                }
                
                currentWallet = null;
                currentPublicKey = null;
                
                updateStatus('✅ 已断开连接', 'info');
                
                document.getElementById('disconnect-btn').disabled = true;
                document.getElementById('phantom-btn').textContent = '连接 Phantom';
                document.getElementById('solflare-btn').textContent = '连接 Solflare';
                
            } catch (error) {
                updateStatus(`❌ 断开连接失败: ${error.message}`, 'error');
            }
        }

        // 页面加载时自动检测钱包
        window.addEventListener('load', () => {
            setTimeout(detectWallets, 1000);
        });
    </script>
</body>
</html>
