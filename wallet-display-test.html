<!DOCTYPE html>
<html>
<head>
    <title>Wallet Display Test</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="assets/css/main.css" />
    <style>
        body {
            background: #1a1a1a;
            color: white;
            font-family: "Source Sans Pro", Helvetica, sans-serif;
            padding: 20px;
        }
        .test-section {
            background: #2a2a2a;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border: 1px solid #444;
        }
        .test-button {
            background: #8b4513;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 5px;
            font-family: "Source Sans Pro", Helvetica, sans-serif;
        }
        .test-button:hover {
            background: #9bf1ff;
            color: #8b4513;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            background: #333;
        }
    </style>
</head>
<body>
    <h1>🧪 Wallet Display Logic Test</h1>
    
    <div class="test-section">
        <h2>Current Wallet Detection Status</h2>
        <button class="test-button" onclick="checkWallets()">Check Installed Wallets</button>
        <div id="wallet-status"></div>
    </div>

    <div class="test-section">
        <h2>Test Wallet Modal</h2>
        <button class="test-button" onclick="openWalletModal()">Open Wallet Modal</button>
        <p>This will show the actual wallet modal with the new logic:</p>
        <ul>
            <li>If wallets are installed: Shows only installed wallets</li>
            <li>If no wallets: Shows installation guide</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>Simulate No Wallets</h2>
        <button class="test-button" onclick="simulateNoWallets()">Simulate No Wallets</button>
        <button class="test-button" onclick="restoreWallets()">Restore Normal Detection</button>
        <p>This temporarily disables wallet detection to test the "no wallets" scenario.</p>
    </div>

    <!-- Include the wallet modal from main page -->
    <div id="wallet-modal">
        <div class="wallet-modal-content">
            <div class="wallet-modal-header">
                <h3 class="wallet-modal-title">Connect Wallet</h3>
                <button id="close-wallet-modal">&times;</button>
            </div>
            <div id="wallet-list">
                <!-- Wallet options will be populated by JavaScript -->
            </div>
        </div>
    </div>

    <script src="assets/js/wallet-adapter.js"></script>
    <script>
        let originalWalletConfigs = null;

        function checkWallets() {
            const statusDiv = document.getElementById('wallet-status');
            const connector = window.bonkWalletConnector;
            
            if (!connector) {
                statusDiv.innerHTML = '<div class="status">❌ Wallet connector not initialized</div>';
                return;
            }

            const installedWallets = connector.supportedWallets.filter(wallet => {
                const provider = wallet.getProvider();
                return provider !== null;
            });

            let html = `<div class="status">
                <strong>Detection Results:</strong><br>
                Total supported wallets: ${connector.supportedWallets.length}<br>
                Installed wallets: ${installedWallets.length}
            </div>`;

            if (installedWallets.length > 0) {
                html += '<div class="status"><strong>Installed Wallets:</strong><br>';
                installedWallets.forEach(wallet => {
                    html += `${wallet.icon} ${wallet.name}<br>`;
                });
                html += '</div>';
            } else {
                html += '<div class="status">❌ No compatible wallets found</div>';
            }

            statusDiv.innerHTML = html;
        }

        function openWalletModal() {
            const connector = window.bonkWalletConnector;
            if (connector) {
                connector.showWalletModal();
            }
        }

        function simulateNoWallets() {
            const connector = window.bonkWalletConnector;
            if (connector && !originalWalletConfigs) {
                // Save original configs
                originalWalletConfigs = [...connector.supportedWallets];
                
                // Replace with configs that return null
                connector.supportedWallets = originalWalletConfigs.map(wallet => ({
                    ...wallet,
                    getProvider: () => null
                }));
                
                alert('✅ Simulating no wallets installed. Try opening the wallet modal now.');
            }
        }

        function restoreWallets() {
            const connector = window.bonkWalletConnector;
            if (connector && originalWalletConfigs) {
                connector.supportedWallets = originalWalletConfigs;
                originalWalletConfigs = null;
                alert('✅ Restored normal wallet detection.');
            }
        }

        // Auto-check on load
        window.addEventListener('load', () => {
            setTimeout(checkWallets, 1000);
        });
    </script>
</body>
</html>
