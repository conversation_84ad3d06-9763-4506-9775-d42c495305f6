<!DOCTYPE HTML>
<html>
<head>
    <title>Wallet Test - Bonk Game</title>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <link rel="stylesheet" href="assets/css/main.css" />
    <style>
        body { 
            padding: 2rem; 
            background: #1a1a1a; 
            color: white; 
            font-family: Arial, sans-serif;
        }
        .test-container {
            max-width: 600px;
            margin: 0 auto;
            text-align: center;
        }
        .test-header {
            margin-bottom: 2rem;
        }
        .status-display {
            margin: 1rem 0;
            padding: 1rem;
            background: #333;
            border-radius: 0.5rem;
            border: 1px solid #555;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>Solana Wallet Adapter Test</h1>
            <p>This page tests the wallet connection functionality.</p>
        </div>

        <div class="wallet-controls">
            <button id="wallet-connect-btn">
                <i class="fas fa-wallet"></i> Connect Wallet
            </button>
        </div>

        <div class="status-display">
            <h3>Connection Status:</h3>
            <div id="status">Disconnected</div>
            <div id="address" style="margin-top: 0.5rem; font-family: monospace;"></div>
        </div>

        <div class="instructions">
            <h3>Instructions:</h3>
            <ol style="text-align: left; max-width: 400px; margin: 0 auto;">
                <li>Click "Connect Wallet" button</li>
                <li>Select your Solana wallet from the modal</li>
                <li>Approve the connection in your wallet extension</li>
                <li>See your wallet address displayed below</li>
                <li>Click the button again to disconnect</li>
            </ol>
        </div>
    </div>

    <!-- Wallet Connect Modal -->
    <div id="wallet-modal">
        <div class="wallet-modal-content">
            <div class="wallet-modal-header">
                <h3 class="wallet-modal-title">Connect Wallet</h3>
                <button id="close-wallet-modal">&times;</button>
            </div>
            <div id="wallet-list">
                <!-- Wallet options will be populated by JavaScript -->
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/js/all.min.js"></script>
    <script src="assets/js/wallet-adapter.js"></script>
    
    <script>
        // Enhanced wallet adapter for testing
        document.addEventListener('DOMContentLoaded', function() {
            // Override the updateUI method to also update our test display
            if (window.solanaWalletAdapter) {
                const originalUpdateUI = window.solanaWalletAdapter.updateUI;
                window.solanaWalletAdapter.updateUI = function() {
                    originalUpdateUI.call(this);
                    
                    const statusDiv = document.getElementById('status');
                    const addressDiv = document.getElementById('address');
                    
                    if (this.connected) {
                        statusDiv.textContent = 'Connected';
                        statusDiv.style.color = '#14F195';
                        addressDiv.textContent = `Address: ${this.publicKey}`;
                        addressDiv.style.color = '#9945FF';
                    } else {
                        statusDiv.textContent = 'Disconnected';
                        statusDiv.style.color = '#888';
                        addressDiv.textContent = '';
                    }
                };
            }
        });
    </script>
</body>
</html>