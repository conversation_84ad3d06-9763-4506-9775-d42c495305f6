<!DOCTYPE html>
<html>
<head>
    <title>钱包按钮测试</title>
    <meta charset="utf-8" />
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #1a1a1a;
            color: white;
        }
        .test-button {
            background: #9945FF;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px;
        }
        .log {
            background: #333;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>🧪 钱包按钮点击测试</h1>
    
    <button class="test-button" onclick="testDirectClick()">直接测试点击</button>
    <button class="test-button" onclick="testWalletButton()">测试钱包按钮</button>
    <button class="test-button" onclick="checkElements()">检查元素</button>
    <button class="test-button" onclick="clearLog()">清空日志</button>
    
    <div id="log" class="log"></div>
    
    <!-- 模拟钱包按钮和模态框 -->
    <div style="margin-top: 20px; border: 1px solid #444; padding: 20px; border-radius: 5px;">
        <h3>模拟钱包UI:</h3>
        <button id="wallet-connect-btn" style="background: #8b4513; color: white; border: none; padding: 8px 16px; cursor: pointer;">
            <i class="fas fa-wallet"></i> Connect Wallet
        </button>
        
        <div id="wallet-modal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.8); z-index: 1000; justify-content: center; align-items: center;">
            <div style="background: #2a2a2a; padding: 20px; border-radius: 10px; max-width: 400px; width: 90%;">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                    <h3 style="color: white; margin: 0;">Connect Wallet</h3>
                    <button id="close-wallet-modal" style="background: none; border: none; color: white; font-size: 24px; cursor: pointer;">&times;</button>
                </div>
                <div id="wallet-list">
                    <div style="padding: 10px; border: 1px solid #444; margin: 5px 0; border-radius: 5px; cursor: pointer;">
                        👻 Phantom - 测试钱包
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        function testDirectClick() {
            log('🖱️ 直接点击测试 - 这个应该能工作');
        }

        function testWalletButton() {
            const button = document.getElementById('wallet-connect-btn');
            if (button) {
                log('✅ 找到钱包按钮，模拟点击');
                button.click();
            } else {
                log('❌ 找不到钱包按钮');
            }
        }

        function checkElements() {
            const button = document.getElementById('wallet-connect-btn');
            const modal = document.getElementById('wallet-modal');
            const closeBtn = document.getElementById('close-wallet-modal');
            
            log('=== 元素检查 ===');
            log(`钱包按钮: ${button ? '✅ 存在' : '❌ 不存在'}`);
            log(`钱包模态框: ${modal ? '✅ 存在' : '❌ 不存在'}`);
            log(`关闭按钮: ${closeBtn ? '✅ 存在' : '❌ 不存在'}`);
            
            if (button) {
                log(`按钮事件监听器数量: ${getEventListeners ? Object.keys(getEventListeners(button)).length : '无法检测'}`);
            }
        }

        // 模拟钱包连接器的基本功能
        function setupTestWallet() {
            const button = document.getElementById('wallet-connect-btn');
            const modal = document.getElementById('wallet-modal');
            const closeBtn = document.getElementById('close-wallet-modal');

            if (button) {
                button.addEventListener('click', (e) => {
                    e.stopPropagation();
                    log('🖱️ 钱包按钮被点击！');
                    if (modal) {
                        log('📱 显示模态框');
                        modal.style.display = 'flex';
                    } else {
                        log('❌ 找不到模态框');
                    }
                });
                log('✅ 钱包按钮事件已绑定');
            }

            if (closeBtn) {
                closeBtn.addEventListener('click', () => {
                    log('❌ 关闭模态框');
                    if (modal) {
                        modal.style.display = 'none';
                    }
                });
            }

            if (modal) {
                modal.addEventListener('click', (e) => {
                    if (e.target === modal) {
                        log('🖱️ 点击模态框外部，关闭');
                        modal.style.display = 'none';
                    }
                });
            }
        }

        // 页面加载后设置测试
        window.addEventListener('load', () => {
            log('🚀 页面加载完成，设置测试钱包');
            setupTestWallet();
            checkElements();
        });
    </script>
</body>
</html>
