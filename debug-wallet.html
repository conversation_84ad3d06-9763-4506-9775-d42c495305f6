<!DOCTYPE html>
<html>
<head>
    <title>钱包调试工具</title>
    <meta charset="utf-8" />
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #1a1a1a;
            color: white;
            line-height: 1.6;
        }
        .debug-section {
            margin: 20px 0;
            padding: 20px;
            background: #2a2a2a;
            border-radius: 10px;
            border: 1px solid #444;
        }
        .button {
            background: #9945FF;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background: #8A3FFC;
        }
        .result {
            background: #333;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .success { border-left: 4px solid #14F195; }
        .error { border-left: 4px solid #ff4545; }
        .warning { border-left: 4px solid #ffa500; }
        .info { border-left: 4px solid #9945FF; }
        h1, h2 { color: #14F195; }
        .install-link {
            color: #14F195;
            text-decoration: none;
            font-weight: bold;
        }
        .install-link:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <h1>🔧 Solana钱包调试工具</h1>
    
    <div class="debug-section">
        <h2>📋 使用说明</h2>
        <p>这个工具帮助您检查Solana钱包的安装和连接状态。</p>
        <p><strong>如果显示"未安装"，请先安装钱包扩展：</strong></p>
        <ul>
            <li><a href="https://phantom.app" target="_blank" class="install-link">安装 Phantom 钱包</a></li>
            <li><a href="https://solflare.com" target="_blank" class="install-link">安装 Solflare 钱包</a></li>
            <li><a href="https://backpack.app" target="_blank" class="install-link">安装 Backpack 钱包</a></li>
        </ul>
        <p><strong>安装后请刷新此页面！</strong></p>
    </div>

    <div class="debug-section">
        <h2>🔍 步骤1：检查Window对象</h2>
        <button class="button" onclick="checkWindowObjects()">检查Window对象</button>
        <div id="window-results" class="result"></div>
    </div>

    <div class="debug-section">
        <h2>🎯 步骤2：钱包检测</h2>
        <button class="button" onclick="detectWallets()">检测钱包</button>
        <div id="detection-results" class="result"></div>
    </div>

    <div class="debug-section">
        <h2>🔌 步骤3：连接测试</h2>
        <button class="button" onclick="testConnection()">测试连接</button>
        <div id="connection-results" class="result"></div>
    </div>

    <script>
        function log(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.className = `result ${type}`;
            element.textContent = message;
        }

        function checkWindowObjects() {
            let result = "=== Window对象检查 ===\n\n";
            
            // 检查所有可能的钱包对象
            const checks = [
                { name: 'window.solana', obj: window.solana },
                { name: 'window.solflare', obj: window.solflare },
                { name: 'window.backpack', obj: window.backpack },
                { name: 'window.slope', obj: window.slope },
                { name: 'window.coin98', obj: window.coin98 },
                { name: 'window.glow', obj: window.glow }
            ];

            checks.forEach(check => {
                if (check.obj) {
                    result += `✅ ${check.name}: 存在\n`;
                    if (check.name === 'window.solana') {
                        result += `   - isPhantom: ${check.obj.isPhantom}\n`;
                        result += `   - isCoinbaseWallet: ${check.obj.isCoinbaseWallet}\n`;
                        result += `   - isTrustWallet: ${check.obj.isTrustWallet}\n`;
                    }
                    if (check.name === 'window.solflare') {
                        result += `   - isSolflare: ${check.obj.isSolflare}\n`;
                    }
                    result += `   - 类型: ${typeof check.obj}\n`;
                    result += `   - 方法: ${Object.getOwnPropertyNames(check.obj).slice(0, 5).join(', ')}\n\n`;
                } else {
                    result += `❌ ${check.name}: 不存在\n\n`;
                }
            });

            // 搜索所有包含solana相关的window属性
            const allKeys = Object.keys(window).filter(key => 
                key.toLowerCase().includes('solana') || 
                key.toLowerCase().includes('phantom') ||
                key.toLowerCase().includes('solflare') ||
                key.toLowerCase().includes('backpack')
            );

            if (allKeys.length > 0) {
                result += "=== 相关Window属性 ===\n";
                allKeys.forEach(key => {
                    result += `- ${key}: ${typeof window[key]}\n`;
                });
            } else {
                result += "⚠️ 未找到任何Solana相关的window属性\n";
                result += "这通常意味着没有安装Solana钱包扩展\n";
            }

            log('window-results', result, allKeys.length > 0 ? 'success' : 'warning');
        }

        function detectWallets() {
            let result = "=== 钱包检测结果 ===\n\n";
            
            const wallets = [
                { 
                    name: 'Phantom', 
                    check: () => window.solana?.isPhantom,
                    url: 'https://phantom.app'
                },
                { 
                    name: 'Solflare', 
                    check: () => window.solflare?.isSolflare,
                    url: 'https://solflare.com'
                },
                { 
                    name: 'Backpack', 
                    check: () => window.backpack,
                    url: 'https://backpack.app'
                }
            ];

            let foundAny = false;
            wallets.forEach(wallet => {
                const isInstalled = wallet.check();
                if (isInstalled) foundAny = true;
                
                result += `${wallet.name}: ${isInstalled ? '✅ 已安装' : '❌ 未安装'}\n`;
                if (!isInstalled) {
                    result += `   安装地址: ${wallet.url}\n`;
                }
                result += '\n';
            });

            if (!foundAny) {
                result += "⚠️ 没有检测到任何Solana钱包\n";
                result += "请安装至少一个钱包扩展后刷新页面\n";
            }

            log('detection-results', result, foundAny ? 'success' : 'error');
        }

        async function testConnection() {
            let result = "=== 连接测试 ===\n\n";
            
            // 测试Phantom
            if (window.solana?.isPhantom) {
                try {
                    result += "正在测试Phantom连接...\n";
                    const response = await window.solana.connect();
                    result += `✅ Phantom连接成功!\n`;
                    result += `地址: ${response.publicKey.toString()}\n\n`;
                    log('connection-results', result, 'success');
                    return;
                } catch (error) {
                    result += `❌ Phantom连接失败: ${error.message}\n\n`;
                }
            }

            // 测试Solflare
            if (window.solflare?.isSolflare) {
                try {
                    result += "正在测试Solflare连接...\n";
                    const response = await window.solflare.connect();
                    result += `✅ Solflare连接成功!\n`;
                    result += `地址: ${response.publicKey.toString()}\n\n`;
                    log('connection-results', result, 'success');
                    return;
                } catch (error) {
                    result += `❌ Solflare连接失败: ${error.message}\n\n`;
                }
            }

            // 测试Backpack
            if (window.backpack) {
                try {
                    result += "正在测试Backpack连接...\n";
                    const response = await window.backpack.connect();
                    result += `✅ Backpack连接成功!\n`;
                    result += `地址: ${response.publicKey.toString()}\n\n`;
                    log('connection-results', result, 'success');
                    return;
                } catch (error) {
                    result += `❌ Backpack连接失败: ${error.message}\n\n`;
                }
            }

            result += "❌ 没有可用的钱包进行连接测试\n";
            result += "请先安装并启用钱包扩展\n";
            log('connection-results', result, 'error');
        }

        // 页面加载时自动检查
        window.addEventListener('load', () => {
            setTimeout(() => {
                checkWindowObjects();
                detectWallets();
            }, 1000);
        });
    </script>
</body>
</html>
