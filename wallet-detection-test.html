<!DOCTYPE html>
<html>
<head>
    <title>Wallet Detection Test</title>
    <meta charset="utf-8" />
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #1a1a1a;
            color: white;
            line-height: 1.6;
        }
        .wallet-test {
            background: #2a2a2a;
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            border-left: 4px solid #444;
        }
        .wallet-test.detected {
            border-left-color: #4CAF50;
        }
        .wallet-test.not-detected {
            border-left-color: #f44336;
        }
        .status {
            font-weight: bold;
            margin-bottom: 10px;
        }
        .detected .status { color: #4CAF50; }
        .not-detected .status { color: #f44336; }
        .details {
            font-size: 0.9em;
            color: #ccc;
            font-family: monospace;
        }
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 5px;
        }
        button:hover {
            background: #45a049;
        }
    </style>
</head>
<body>
    <h1>🔍 Wallet Detection Test</h1>
    <p>This page tests the detection of various Solana wallets.</p>
    
    <button onclick="runDetection()">🔄 Run Detection</button>
    <button onclick="showWindowProperties()">🪟 Show Window Properties</button>
    
    <div id="results"></div>
    
    <div id="window-props" style="margin-top: 20px;"></div>

    <script>
        const walletConfigs = [
            {
                name: 'Phantom',
                key: 'phantom',
                icon: '👻',
                getProvider: () => {
                    if (window.solana?.isPhantom) return window.solana;
                    if (window.phantom?.solana?.isPhantom) return window.phantom.solana;
                    return null;
                },
                checkPaths: ['window.solana', 'window.phantom.solana']
            },
            {
                name: 'OKX Wallet',
                key: 'okx',
                icon: '⭕',
                getProvider: () => {
                    if (window.okxwallet?.solana) return window.okxwallet.solana;
                    if (window.okx?.solana) return window.okx.solana;
                    return null;
                },
                checkPaths: ['window.okxwallet.solana', 'window.okx.solana']
            },
            {
                name: 'Solflare',
                key: 'solflare',
                icon: '🔥',
                getProvider: () => {
                    if (window.solflare?.isSolflare) return window.solflare;
                    if (window.SolflareApp) return window.SolflareApp;
                    return null;
                },
                checkPaths: ['window.solflare', 'window.SolflareApp']
            },
            {
                name: 'Backpack',
                key: 'backpack',
                icon: '🎒',
                getProvider: () => {
                    if (window.backpack?.isBackpack) return window.backpack;
                    if (window.xnft?.solana) return window.xnft.solana;
                    return null;
                },
                checkPaths: ['window.backpack', 'window.xnft.solana']
            }
        ];

        function runDetection() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<h2>🔍 Detection Results</h2>';
            
            walletConfigs.forEach(wallet => {
                const provider = wallet.getProvider();
                const isDetected = provider !== null;
                
                const walletDiv = document.createElement('div');
                walletDiv.className = `wallet-test ${isDetected ? 'detected' : 'not-detected'}`;
                
                let pathsInfo = '';
                wallet.checkPaths.forEach(path => {
                    const exists = checkPath(path);
                    pathsInfo += `${path}: ${exists ? '✅' : '❌'}\n`;
                });
                
                walletDiv.innerHTML = `
                    <div class="status">${wallet.icon} ${wallet.name}: ${isDetected ? '✅ DETECTED' : '❌ NOT DETECTED'}</div>
                    <div class="details">
                        Paths checked:\n${pathsInfo}
                        ${provider ? `Provider object: ${typeof provider}\n` : ''}
                        ${provider?.isConnected !== undefined ? `Connected: ${provider.isConnected}\n` : ''}
                        ${provider?.publicKey ? `Public Key: ${provider.publicKey.toString()}\n` : ''}
                    </div>
                `;
                
                resultsDiv.appendChild(walletDiv);
            });
        }

        function checkPath(path) {
            try {
                const parts = path.split('.');
                let obj = window;
                for (let i = 1; i < parts.length; i++) {
                    if (obj && obj[parts[i]] !== undefined) {
                        obj = obj[parts[i]];
                    } else {
                        return false;
                    }
                }
                return obj !== undefined && obj !== null;
            } catch (e) {
                return false;
            }
        }

        function showWindowProperties() {
            const propsDiv = document.getElementById('window-props');
            const relevantProps = Object.keys(window).filter(key => 
                key.toLowerCase().includes('solana') ||
                key.toLowerCase().includes('phantom') ||
                key.toLowerCase().includes('solflare') ||
                key.toLowerCase().includes('backpack') ||
                key.toLowerCase().includes('okx') ||
                key.toLowerCase().includes('xnft')
            );
            
            propsDiv.innerHTML = `
                <h3>🪟 Relevant Window Properties</h3>
                <div class="details">
                    Found properties: ${relevantProps.length}\n
                    ${relevantProps.map(prop => `${prop}: ${typeof window[prop]}`).join('\n')}
                </div>
            `;
        }

        // Auto-run detection when page loads
        window.addEventListener('load', () => {
            setTimeout(runDetection, 1000);
        });
    </script>
</body>
</html>
