#!/usr/bin/env python3
"""
最简单的HTTP服务器
解决钱包扩展注入问题
"""

import http.server
import socketserver
import os

PORT = 3000

# 切换到脚本所在目录
os.chdir(os.path.dirname(os.path.abspath(__file__)))

print("🚀 启动Bonk Game本地服务器...")
print(f"📁 服务目录: {os.getcwd()}")

try:
    with socketserver.TCPServer(("", PORT), http.server.SimpleHTTPRequestHandler) as httpd:
        print(f"✅ 服务器启动成功!")
        print(f"📍 访问地址: http://localhost:{PORT}")
        print(f"🔗 调试页面: http://localhost:{PORT}/debug-wallet.html")
        print(f"💡 按 Ctrl+C 停止服务器")
        print("-" * 50)
        httpd.serve_forever()
except KeyboardInterrupt:
    print("\n👋 服务器已停止")
except Exception as e:
    print(f"❌ 启动失败: {e}")
    print("💡 请检查端口是否被占用或尝试其他端口")
