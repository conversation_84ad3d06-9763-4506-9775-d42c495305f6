<!DOCTYPE HTML>
<html>
<head>
    <title>直接钱包连接器演示 - Bonk Game</title>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <link rel="stylesheet" href="assets/css/main.css" />
    <style>
        .demo-container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 2rem;
            background: #1a1a1a;
            color: white;
            font-family: Arial, sans-serif;
        }
        
        .demo-section {
            background: #2a2a2a;
            padding: 2rem;
            border-radius: 1rem;
            margin: 2rem 0;
            border: 1px solid #444;
        }
        
        .demo-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .demo-header h1 {
            background: linear-gradient(135deg, #9945FF, #14F195);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1rem;
            margin: 2rem 0;
        }
        
        .status-card {
            background: #333;
            padding: 1.5rem;
            border-radius: 0.5rem;
            border: 1px solid #555;
        }
        
        .status-card h3 {
            margin: 0 0 1rem 0;
            color: #9945FF;
        }
        
        .status-value {
            font-family: monospace;
            font-size: 0.9rem;
            color: #14F195;
            word-break: break-all;
        }
        
        .button-group {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
            margin: 2rem 0;
        }
        
        .demo-button {
            background: linear-gradient(135deg, #9945FF, #14F195);
            color: white;
            border: none;
            padding: 0.8rem 1.5rem;
            border-radius: 0.5rem;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 150px;
        }
        
        .demo-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(153, 69, 255, 0.3);
        }
        
        .demo-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }
        
        .code-example {
            background: #1a1a1a;
            padding: 1rem;
            border-radius: 0.3rem;
            border: 1px solid #444;
            overflow-x: auto;
            margin: 1rem 0;
        }
        
        .code-example pre {
            margin: 0;
            font-family: monospace;
            font-size: 0.9rem;
            color: #e0e0e0;
        }
        
        .log-output {
            background: #1a1a1a;
            padding: 1rem;
            border-radius: 0.3rem;
            border: 1px solid #444;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 0.8rem;
            color: #ccc;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
        }
        
        .feature-list li {
            padding: 0.5rem 0;
            border-bottom: 1px solid #444;
        }
        
        .feature-list li:last-child {
            border-bottom: none;
        }
        
        .feature-list li::before {
            content: "✅ ";
            color: #14F195;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-header">
            <h1>🎮 Bonk Game 直接钱包连接器</h1>
            <p>演示如何使用vanilla JavaScript直接集成Solana钱包</p>
        </div>

        <!-- 钱包连接区域 -->
        <div class="demo-section">
            <h2>🔗 钱包连接</h2>
            <div class="button-group">
                <button id="wallet-connect-btn" class="demo-button">
                    <i class="fas fa-wallet"></i> Connect Wallet
                </button>
                <button id="get-balance-btn" class="demo-button">
                    💰 Get Balance
                </button>
                <button id="detect-wallets-btn" class="demo-button">
                    🔍 Detect Wallets
                </button>
            </div>
        </div>

        <!-- 状态显示 -->
        <div class="status-grid">
            <div class="status-card">
                <h3>连接状态</h3>
                <div id="connection-status" class="status-value">未连接</div>
            </div>
            <div class="status-card">
                <h3>钱包类型</h3>
                <div id="wallet-type" class="status-value">-</div>
            </div>
            <div class="status-card">
                <h3>钱包地址</h3>
                <div id="wallet-address" class="status-value">-</div>
            </div>
            <div class="status-card">
                <h3>余额</h3>
                <div id="wallet-balance" class="status-value">-</div>
            </div>
        </div>

        <!-- 技术说明 -->
        <div class="demo-section">
            <h2>🛠️ 技术实现</h2>
            <p>这个演示展示了如何使用vanilla JavaScript直接与Solana钱包交互，无需React或复杂的构建工具。</p>
            
            <h3>核心原理</h3>
            <div class="code-example">
                <pre>
// 检测Phantom钱包
const phantom = window.solana?.isPhantom ? window.solana : null;

// 连接钱包
const response = await phantom.connect();
const publicKey = response.publicKey.toString();

// 监听事件
phantom.on('connect', () => console.log('Connected'));
phantom.on('disconnect', () => console.log('Disconnected'));
                </pre>
            </div>

            <h3>✅ 优势</h3>
            <ul class="feature-list">
                <li>无需React或构建工具</li>
                <li>代码简洁，易于理解</li>
                <li>直接使用钱包官方API</li>
                <li>支持所有主流Solana钱包</li>
                <li>性能优秀，加载速度快</li>
                <li>适合静态HTML网站</li>
            </ul>
        </div>

        <!-- 支持的钱包 -->
        <div class="demo-section">
            <h2>📱 支持的钱包</h2>
            <div id="supported-wallets">
                <!-- 钱包列表将由JavaScript动态生成 -->
            </div>
        </div>

        <!-- 日志输出 -->
        <div class="demo-section">
            <h2>📋 实时日志</h2>
            <div id="log-output" class="log-output">
                等待操作...
            </div>
            <button id="clear-log-btn" class="demo-button">清除日志</button>
        </div>
    </div>

    <!-- 钱包连接模态框 -->
    <div id="wallet-modal">
        <div class="wallet-modal-content">
            <div class="wallet-modal-header">
                <h3 class="wallet-modal-title">选择钱包</h3>
                <button id="close-wallet-modal">&times;</button>
            </div>
            <div id="wallet-list">
                <!-- 钱包列表将由JavaScript动态生成 -->
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/js/all.min.js"></script>
    <script src="https://unpkg.com/@solana/web3.js@latest/lib/index.iife.min.js"></script>
    <script src="assets/js/direct-wallet-connector.js"></script>
    
    <script>
        // 演示页面控制器
        class WalletDemo {
            constructor() {
                this.logOutput = document.getElementById('log-output');
                this.init();
            }

            init() {
                this.setupEventListeners();
                this.setupWalletEvents();
                this.log('🚀 钱包演示页面已初始化');
            }

            setupEventListeners() {
                // 获取余额按钮
                document.getElementById('get-balance-btn').addEventListener('click', async () => {
                    await this.getBalance();
                });

                // 检测钱包按钮
                document.getElementById('detect-wallets-btn').addEventListener('click', () => {
                    this.detectWallets();
                });

                // 清除日志按钮
                document.getElementById('clear-log-btn').addEventListener('click', () => {
                    this.clearLog();
                });
            }

            setupWalletEvents() {
                // 监听钱包连接事件
                document.addEventListener('walletConnected', (event) => {
                    this.log(`✅ 钱包连接成功: ${event.detail.walletType}`);
                    this.updateStatus();
                });

                document.addEventListener('walletDisconnected', () => {
                    this.log('🔌 钱包已断开连接');
                    this.updateStatus();
                });

                // 定期更新状态
                setInterval(() => {
                    this.updateStatus();
                }, 1000);
            }

            async getBalance() {
                if (!window.directWalletConnector || !window.directWalletConnector.connected) {
                    this.log('❌ 请先连接钱包');
                    return;
                }

                try {
                    this.log('💰 正在获取余额...');
                    const balance = await window.directWalletConnector.getBalance();
                    
                    if (balance !== null) {
                        this.log(`💰 余额: ${balance.toFixed(4)} SOL`);
                        document.getElementById('wallet-balance').textContent = `${balance.toFixed(4)} SOL`;
                    } else {
                        this.log('❌ 获取余额失败');
                    }
                } catch (error) {
                    this.log(`❌ 获取余额错误: ${error.message}`);
                }
            }

            detectWallets() {
                this.log('🔍 正在检测钱包...');
                
                const wallets = [
                    { name: 'Phantom', check: () => window.solana?.isPhantom },
                    { name: 'Solflare', check: () => window.solflare?.isSolflare },
                    { name: 'Backpack', check: () => window.backpack },
                    { name: 'Slope', check: () => window.slope },
                    { name: 'Coin98', check: () => window.coin98 },
                    { name: 'Glow', check: () => window.glow }
                ];

                wallets.forEach(wallet => {
                    const isInstalled = wallet.check();
                    this.log(`${wallet.name}: ${isInstalled ? '✅ 已安装' : '❌ 未安装'}`);
                });

                this.updateSupportedWallets();
            }

            updateSupportedWallets() {
                const supportedWalletsDiv = document.getElementById('supported-wallets');
                const wallets = [
                    { name: 'Phantom', icon: '👻', check: () => window.solana?.isPhantom },
                    { name: 'Solflare', icon: '🔥', check: () => window.solflare?.isSolflare },
                    { name: 'Backpack', icon: '🎒', check: () => window.backpack }
                ];

                supportedWalletsDiv.innerHTML = wallets.map(wallet => {
                    const isInstalled = wallet.check();
                    return `
                        <div style="display: flex; align-items: center; padding: 0.5rem; border-bottom: 1px solid #444;">
                            <span style="font-size: 1.5rem; margin-right: 1rem;">${wallet.icon}</span>
                            <span style="flex: 1;">${wallet.name}</span>
                            <span style="color: ${isInstalled ? '#14F195' : '#888'};">
                                ${isInstalled ? '✅ 已安装' : '❌ 未安装'}
                            </span>
                        </div>
                    `;
                }).join('');
            }

            updateStatus() {
                const connector = window.directWalletConnector;
                if (!connector) return;

                document.getElementById('connection-status').textContent = 
                    connector.connected ? '✅ 已连接' : '❌ 未连接';
                
                document.getElementById('wallet-type').textContent = 
                    connector.walletType || '-';
                
                document.getElementById('wallet-address').textContent = 
                    connector.publicKey || '-';
            }

            log(message) {
                const timestamp = new Date().toLocaleTimeString();
                const logLine = `[${timestamp}] ${message}`;
                
                this.logOutput.innerHTML += logLine + '<br>';
                this.logOutput.scrollTop = this.logOutput.scrollHeight;
                
                console.log(logLine);
            }

            clearLog() {
                this.logOutput.innerHTML = '';
                this.log('📋 日志已清除');
            }
        }

        // 页面加载完成后初始化演示
        document.addEventListener('DOMContentLoaded', () => {
            // 等待钱包连接器初始化
            setTimeout(() => {
                window.walletDemo = new WalletDemo();
            }, 500);
        });
    </script>
</body>
</html>