<!DOCTYPE HTML>
<!--
	Forty by HTML5 UP
	html5up.net | @ajlkn
	Free for personal and commercial use under the CCA 3.0 license (html5up.net/license)
-->
<html>
	
<!-- Mirrored from bonkgame.com/games/snow-panda by HTTrack Website Copier/3.x [XR&CO'2014], Wed, 09 Jul 2025 09:04:08 GMT -->
<!-- Added by HTTrack --><meta http-equiv="content-type" content="text/html;charset=utf-8" /><!-- /Added by HTTrack -->
<head>
		<title>Snow Panda</title>
        <link rel="icon" type="image/png" href="../images/bonkgame%20logo.jpg">
		<meta charset="utf-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no" />
		<link rel="stylesheet" href="../assets/css/main.css" />
		<noscript><link rel="stylesheet" href="../assets/css/noscript.css" /></noscript>
		<script type="application/javascript" src="http://anymind360.com/js/16468/ats.js"></script>
		<!-- Google tag (gtag.js) -->
		<script async src="https://www.googletagmanager.com/gtag/js?id=G-D5NFENPE8G"></script>
		<script>
		  window.dataLayer = window.dataLayer || [];
		  function gtag(){dataLayer.push(arguments);}
		  gtag('js', new Date());
		
		  gtag('config', 'G-D5NFENPE8G');
		</script>
	</head>
	<body class="is-preload">

		<!-- Wrapper -->
			<div id="wrapper">

				<!-- Header -->
					<header id="header">
						<a href="../index.html" class="logo"><img src="../images/bonkgame%20logo.jpg" alt="Bonk-Game-Logo" style="height: 50px; vertical-align: middle;">
                        <strong>Bonk Game</strong></a>
						<nav>
							<a href="#menu">Menu</a>
							<button id="wallet-connect-btn">
								<i class="fas fa-wallet"></i> Connect Wallet
							</button>
						</nav>
					</header>

				<!-- Menu -->
					<nav id="menu">
						<ul class="links">
							<li><a href="../index.html">Home</a></li>
						</ul>
					</nav>

				<!-- Main -->
					<div id="main" class="alt">

						<!-- One -->
							<section id="one">
								<div class="inner">
									<header class="major">
										<h1>Snow Panda</h1>
										<H4>By: Gamelauncher</H4>
									</header>
									<div class="iframe-container">
										<iframe src="https://html5.gamemonetize.co/vtav9j83j8w7a9tzsgzq2766jb7100vo/" scrolling="none"></iframe>
									</div>
									<h2>Description</h2>
									<p>Help the beer to collect the honey, logical brain game.
Tap to create blocks as required in the game level, height should be upto the honey. 
Don’t miss to create blocks as collisions with walls will over the game.
If you like genres such as logical brain game or just want to have a good time then this game is for you.
Features of this game:
High Quality graphics will leave only positive feedback
Game size is minimal (but not compromised the graphics) and it is good for your device health
Super simple controls make it easy to start playing
Interesting levels will not let you get bored while playing the game.</p>
									<h2>Walkthroughs</h2>
									<div id="gamemonetize-video"></div>
									<script type="text/javascript">
									   window.VIDEO_OPTIONS = {
										   gameid : "vtav9j83j8w7a9tzsgzq2766jb7100vo",
										   width  : "100%",
										   height : "480px",
										   color  : "#3f007e"
									   };
									   (function (a, b, c) {
										  var d = a.getElementsByTagName(b)[0];
										  a.getElementById(c) || (a = a.createElement(b), a.id = c, a.src = "https://api.gamemonetize.com/video.js?v=" + Date.now(), d.parentNode.insertBefore(a, d))
									   })(document, "script", "gamemonetize-video-api"); 
									</script>
								</div>
							</section>

							<section id="three">
								<div class="inner">
									<header class="major">
                                <h2>Take a break! Enjoy a video</h2>
                                <strong>Featured with consent from the creator</strong>
                                </header>
                                <div class="video-container">
                                    <iframe id="ytPlayer" width="640" height="360" 
                                        src="https://www.youtube.com/embed/0xyUOOWSuUg?enablejsapi=1" 
                                        frameborder="0" allow="accelerometer; autoplay; clipboard-write; 
                                        encrypted-media; gyroscope; picture-in-picture" allowfullscreen="">
                                    </iframe>
                                </div>
                            </section>                 
					</div>

				<!-- Wallet Connect Modal -->
				<div id="wallet-modal">
					<div class="wallet-modal-content">
						<div class="wallet-modal-header">
							<h3 class="wallet-modal-title">Connect Wallet</h3>
							<button id="close-wallet-modal">&times;</button>
						</div>
						<div id="wallet-list">
							<!-- Wallet options will be populated by JavaScript -->
						</div>
					</div>
				</div>

			<!-- Footer -->
			<footer id="footer">
				<div class="inner">
					<ul class="copyright">
						<li>&copy; Copyright @ Bonk Game</li><li><a href="../privacy%20policy.html">Privacy Policy</a></li><li><a href="../term%20of%20use.html">Term of Use</a></li><li>Contact</li>
					</ul>
				</div>
			</footer>
		</div>

	<!-- Scripts -->
		<script src="../assets/js/jquery.min.js"></script>
		<script src="../assets/js/jquery.scrolly.min.js"></script>
		<script src="../assets/js/jquery.scrollex.min.js"></script>
		<script src="../assets/js/browser.min.js"></script>
		<script src="../assets/js/breakpoints.min.js"></script>
		<script src="../assets/js/util.js"></script>
		<script src="../assets/js/main.js"></script>
		<script src="../assets/js/wallet-adapter.js"></script>


<!-- Mirrored from bonkgame.com/games/snow-panda by HTTrack Website Copier/3.x [XR&CO'2014], Wed, 09 Jul 2025 09:04:10 GMT -->
</html>