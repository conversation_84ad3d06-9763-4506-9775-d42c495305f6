#!/usr/bin/env node
/**
 * 简单的HTTP服务器 - Node.js版本
 * 解决file://协议导致的钱包扩展注入问题
 */

const http = require('http');
const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');

const PORT = 3000;

// MIME类型映射
const mimeTypes = {
    '.html': 'text/html',
    '.js': 'text/javascript',
    '.css': 'text/css',
    '.json': 'application/json',
    '.png': 'image/png',
    '.jpg': 'image/jpg',
    '.gif': 'image/gif',
    '.svg': 'image/svg+xml',
    '.wav': 'audio/wav',
    '.mp4': 'video/mp4',
    '.woff': 'application/font-woff',
    '.ttf': 'application/font-ttf',
    '.eot': 'application/vnd.ms-fontobject',
    '.otf': 'application/font-otf',
    '.wasm': 'application/wasm'
};

const server = http.createServer((req, res) => {
    // 添加CORS头部，确保钱包扩展可以正常工作
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', '*');
    res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
    res.setHeader('Pragma', 'no-cache');
    res.setHeader('Expires', '0');

    // 处理OPTIONS请求
    if (req.method === 'OPTIONS') {
        res.writeHead(200);
        res.end();
        return;
    }

    // 解析请求路径
    let filePath = '.' + req.url;
    if (filePath === './') {
        filePath = './index.html';
    }

    // 获取文件扩展名
    const extname = String(path.extname(filePath)).toLowerCase();
    const mimeType = mimeTypes[extname] || 'application/octet-stream';

    // 读取文件
    fs.readFile(filePath, (error, content) => {
        if (error) {
            if (error.code === 'ENOENT') {
                // 文件不存在
                res.writeHead(404, { 'Content-Type': 'text/html' });
                res.end(`
                    <h1>404 - 文件未找到</h1>
                    <p>请求的文件 ${req.url} 不存在</p>
                    <p><a href="/">返回首页</a></p>
                `, 'utf-8');
            } else {
                // 服务器错误
                res.writeHead(500);
                res.end(`服务器错误: ${error.code}`);
            }
        } else {
            // 成功返回文件
            res.writeHead(200, { 'Content-Type': mimeType });
            res.end(content, 'utf-8');
        }
    });
});

server.listen(PORT, () => {
    console.log('🚀 Bonk Game 本地服务器启动成功!');
    console.log(`📍 访问地址: http://localhost:${PORT}`);
    console.log(`📁 服务目录: ${process.cwd()}`);
    console.log('');
    console.log('🔗 可用页面:');
    console.log(`   主页面: http://localhost:${PORT}/index.html`);
    console.log(`   调试页面: http://localhost:${PORT}/debug-wallet.html`);
    console.log(`   简单测试: http://localhost:${PORT}/wallet-simple-test.html`);
    console.log('');
    console.log('💡 使用 Ctrl+C 停止服务器');
    console.log('🌐 正在自动打开调试页面...');
    
    // 自动打开浏览器
    setTimeout(() => {
        const url = `http://localhost:${PORT}/debug-wallet.html`;
        const start = process.platform === 'darwin' ? 'open' : 
                     process.platform === 'win32' ? 'start' : 'xdg-open';
        exec(`${start} ${url}`);
    }, 1000);
});

server.on('error', (err) => {
    if (err.code === 'EADDRINUSE') {
        console.log(`❌ 端口 ${PORT} 已被占用`);
        console.log('💡 解决方案:');
        console.log('   1. 关闭其他占用该端口的程序');
        console.log('   2. 或者修改 PORT 变量使用其他端口');
    } else {
        console.log(`❌ 服务器错误: ${err}`);
    }
});

process.on('SIGINT', () => {
    console.log('\n👋 服务器已停止');
    process.exit(0);
});
