#!/bin/bash

# Script to add Solana wallet adapter to all game pages
# Usage: ./add_wallet_to_games.sh

GAMES_DIR="games"
WALLET_BUTTON='							<button id="wallet-connect-btn">
								<i class="fas fa-wallet"></i> Connect Wallet
							</button>'

WALLET_MODAL='				<!-- Wallet Connect Modal -->
				<div id="wallet-modal">
					<div class="wallet-modal-content">
						<div class="wallet-modal-header">
							<h3 class="wallet-modal-title">Connect Wallet</h3>
							<button id="close-wallet-modal">&times;</button>
						</div>
						<div id="wallet-list">
							<!-- Wallet options will be populated by JavaScript -->
						</div>
					</div>
				</div>'

WALLET_SCRIPT='		<script src="../assets/js/wallet-adapter.js"></script>'

echo "Adding Solana wallet adapter to all game pages..."

# Counter for processed files
count=0

# Process each HTML file in the games directory
for file in "$GAMES_DIR"/*.html; do
    if [ -f "$file" ]; then
        echo "Processing: $file"
        
        # Create backup
        cp "$file" "$file.backup"
        
        # Add wallet button to nav (after Menu link)
        sed -i 's|<a href="#menu">Menu</a>|<a href="#menu">Menu</a>\
'"$WALLET_BUTTON"'|' "$file"
        
        # Add wallet modal before footer
        sed -i 's|<!-- Footer -->|'"$WALLET_MODAL"'\
\
			<!-- Footer -->|' "$file"
        
        # Add wallet script after main.js
        sed -i 's|<script src="../assets/js/main.js"></script>|<script src="../assets/js/main.js"></script>\
'"$WALLET_SCRIPT"'|' "$file"
        
        ((count++))
    fi
done

echo "Processed $count game pages"
echo "Backup files created with .backup extension"
echo "Wallet adapter integration complete!"

# Instructions
echo ""
echo "To test the integration:"
echo "1. Open any game page in a browser"
echo "2. Look for the 'Connect Wallet' button in the header"
echo "3. Click it to test wallet connection functionality"
echo ""
echo "To revert changes:"
echo "for file in games/*.backup; do mv \"\$file\" \"\${file%.backup}\"; done"