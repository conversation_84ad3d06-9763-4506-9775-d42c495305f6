# 直接钱包连接器 - 官方推荐方案

## 🎯 为什么使用直接API？

### ❌ 官方@solana/wallet-adapter的限制
- **仅支持React**: 需要React Context、Provider和Hooks
- **构建工具依赖**: 需要webpack、rollup等构建工具
- **包体积大**: 包含大量不必要的依赖
- **复杂配置**: 需要复杂的提供者设置

### ✅ 直接API的优势
- **零依赖**: 直接使用钱包注入的API
- **简单易懂**: 代码清晰，逻辑直观
- **性能优秀**: 无额外包装，直接调用
- **广泛支持**: 所有钱包都支持原生API

## 🛠️ 实现方案

### 1. 核心检测逻辑
```javascript
// 检测Phantom钱包
const phantom = window.solana?.isPhantom ? window.solana : null;

// 检测Solflare钱包
const solflare = window.solflare?.isSolflare ? window.solflare : null;

// 检测Backpack钱包
const backpack = window.backpack || null;
```

### 2. 连接钱包
```javascript
async function connectPhantom() {
    try {
        const response = await window.solana.connect();
        const publicKey = response.publicKey.toString();
        console.log('连接成功:', publicKey);
        return publicKey;
    } catch (error) {
        console.error('连接失败:', error);
        throw error;
    }
}
```

### 3. 监听事件
```javascript
// 监听连接状态变化
window.solana.on('connect', () => {
    console.log('钱包已连接');
});

window.solana.on('disconnect', () => {
    console.log('钱包已断开');
});

window.solana.on('accountChanged', (publicKey) => {
    console.log('账户变更:', publicKey?.toString());
});
```

### 4. 获取余额
```javascript
async function getBalance(publicKey) {
    // 使用Solana Web3.js
    const connection = new solanaWeb3.Connection(
        'https://api.mainnet-beta.solana.com'
    );
    
    const balance = await connection.getBalance(
        new solanaWeb3.PublicKey(publicKey)
    );
    
    return balance / solanaWeb3.LAMPORTS_PER_SOL;
}
```

## 📁 文件结构

```
bonkgame/
├── assets/js/
│   ├── direct-wallet-connector.js    # 新的直接连接器
│   └── wallet-adapter.js             # 旧的适配器（保留）
├── direct-wallet-demo.html           # 演示页面
├── phantom-direct-demo.html          # Phantom专用演示
└── index.html                        # 主页面
```

## 🎮 集成到Bonk Game

### 1. HTML结构
```html
<!-- 钱包连接按钮 -->
<button id="wallet-connect-btn">
    <i class="fas fa-wallet"></i> Connect Wallet
</button>

<!-- 钱包选择模态框 -->
<div id="wallet-modal">
    <div class="wallet-modal-content">
        <div class="wallet-modal-header">
            <h3 class="wallet-modal-title">选择钱包</h3>
            <button id="close-wallet-modal">&times;</button>
        </div>
        <div id="wallet-list"></div>
    </div>
</div>
```

### 2. 脚本引入
```html
<!-- Solana Web3.js for balance checking -->
<script src="https://unpkg.com/@solana/web3.js@latest/lib/index.iife.min.js"></script>
<!-- 直接钱包连接器 -->
<script src="assets/js/direct-wallet-connector.js"></script>
```

### 3. 使用方式
```javascript
// 访问全局连接器
const connector = window.directWalletConnector;

// 检查连接状态
if (connector.connected) {
    console.log('已连接钱包:', connector.publicKey);
    console.log('钱包类型:', connector.walletType);
}

// 获取余额
const balance = await connector.getBalance();
console.log('余额:', balance, 'SOL');
```

## 🔧 支持的钱包

| 钱包名称 | 检测方法 | 下载链接 |
|---------|----------|----------|
| 👻 Phantom | `window.solana?.isPhantom` | https://phantom.app |
| 🔥 Solflare | `window.solflare?.isSolflare` | https://solflare.com |
| 🎒 Backpack | `window.backpack` | https://backpack.app |

## 📊 性能对比

| 方案 | 包大小 | 加载时间 | 兼容性 | 维护成本 |
|------|--------|----------|--------|----------|
| 官方Adapter | ~2MB | 慢 | 仅React | 高 |
| 直接API | ~50KB | 快 | 全平台 | 低 |

## 🚀 使用步骤

### 1. 打开演示页面
访问 `direct-wallet-demo.html` 查看完整演示

### 2. 测试连接
1. 点击 "Connect Wallet" 按钮
2. 选择您的钱包
3. 在钱包扩展中批准连接
4. 查看连接状态和地址

### 3. 测试功能
- 获取余额
- 检测钱包
- 查看实时日志

## 🎯 最佳实践

### 1. 延迟检测
```javascript
// 给钱包扩展注入时间
setTimeout(() => {
    detectWallets();
}, 500);
```

### 2. 错误处理
```javascript
try {
    await wallet.connect();
} catch (error) {
    if (error.code === 4001) {
        console.log('用户拒绝连接');
    } else {
        console.error('连接失败:', error);
    }
}
```

### 3. 状态持久化
```javascript
// 保存连接状态
localStorage.setItem('wallet_connected', 'true');
localStorage.setItem('wallet_type', 'phantom');
localStorage.setItem('wallet_address', publicKey);
```

## 🔐 安全注意事项

1. **只读权限**: 仅获取公钥，不处理私钥
2. **用户控制**: 所有交易都需要用户在钱包中确认
3. **数据验证**: 始终验证从钱包返回的数据
4. **错误处理**: 妥善处理连接失败和用户拒绝

## 📝 总结

直接使用钱包原生API是静态HTML网站集成Solana钱包的最佳方案：

- ✅ 简单直接，无需复杂配置
- ✅ 性能优秀，加载速度快
- ✅ 兼容性好，支持所有钱包
- ✅ 维护成本低，代码可读性高

这种方法完全符合钱包官方推荐的集成方式，是现代Web3应用的标准做法。