<!DOCTYPE html>
<html>
<head>
    <title>简单钱包测试 - Bonk Game</title>
    <meta charset="utf-8" />
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #1a1a1a;
            color: white;
            max-width: 600px;
            margin: 0 auto;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            background: #2a2a2a;
            border-radius: 10px;
            border: 1px solid #444;
        }
        .wallet-button {
            background: linear-gradient(135deg, #9945FF, #14F195);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: all 0.3s ease;
        }
        .wallet-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(153, 69, 255, 0.3);
        }
        .wallet-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            font-family: monospace;
            font-size: 14px;
        }
        .success { background: rgba(20, 241, 149, 0.1); border: 1px solid #14F195; }
        .error { background: rgba(255, 69, 69, 0.1); border: 1px solid #ff4545; }
        .info { background: rgba(153, 69, 255, 0.1); border: 1px solid #9945FF; }
        .detection-result {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            margin: 5px 0;
            background: #333;
            border-radius: 5px;
        }
        .wallet-name {
            font-weight: bold;
        }
        .wallet-status {
            font-size: 12px;
        }
        .installed { color: #14F195; }
        .not-installed { color: #ff4545; }
    </style>
</head>
<body>
    <h1>🧪 简单钱包连接测试</h1>
    
    <div class="test-section">
        <h2>📋 说明</h2>
        <div class="status info">
            这是一个简化的测试页面，用于验证我们的钱包连接器是否正常工作。<br>
            我们使用钱包的原生API，不依赖@solana/wallet-adapter。
        </div>
    </div>

    <div class="test-section">
        <h2>🔍 钱包检测</h2>
        <button class="wallet-button" onclick="detectWallets()">检测钱包</button>
        <div id="detection-results"></div>
    </div>

    <div class="test-section">
        <h2>🔌 直接连接测试</h2>
        <button class="wallet-button" onclick="testPhantom()">测试 Phantom</button>
        <button class="wallet-button" onclick="testSolflare()">测试 Solflare</button>
        <button class="wallet-button" onclick="testBackpack()">测试 Backpack</button>
        <div id="connection-results"></div>
    </div>

    <script>
        function updateResults(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        function detectWallets() {
            const wallets = [
                { 
                    name: '👻 Phantom', 
                    check: () => window.solana?.isPhantom,
                    obj: 'window.solana'
                },
                { 
                    name: '🔥 Solflare', 
                    check: () => window.solflare?.isSolflare,
                    obj: 'window.solflare'
                },
                { 
                    name: '🎒 Backpack', 
                    check: () => window.backpack,
                    obj: 'window.backpack'
                }
            ];

            let html = '<h3>检测结果：</h3>';
            let foundAny = false;

            wallets.forEach(wallet => {
                const isInstalled = wallet.check();
                if (isInstalled) foundAny = true;
                
                html += `<div class="detection-result">
                    <span class="wallet-name">${wallet.name}</span>
                    <span class="wallet-status ${isInstalled ? 'installed' : 'not-installed'}">
                        ${isInstalled ? '✅ 已安装' : '❌ 未安装'}
                    </span>
                </div>`;
            });

            if (!foundAny) {
                html += '<div class="status error">⚠️ 未检测到任何Solana钱包。请安装Phantom、Solflare或Backpack钱包扩展。</div>';
            }

            document.getElementById('detection-results').innerHTML = html;
        }

        async function testPhantom() {
            try {
                if (!window.solana || !window.solana.isPhantom) {
                    updateResults('connection-results', '❌ Phantom钱包未安装', 'error');
                    return;
                }

                updateResults('connection-results', '🔄 正在连接Phantom...', 'info');
                const response = await window.solana.connect();
                
                updateResults('connection-results', 
                    `✅ Phantom连接成功！<br>地址: ${response.publicKey.toString()}`, 
                    'success'
                );
            } catch (error) {
                updateResults('connection-results', 
                    `❌ Phantom连接失败: ${error.message}`, 
                    'error'
                );
            }
        }

        async function testSolflare() {
            try {
                if (!window.solflare || !window.solflare.isSolflare) {
                    updateResults('connection-results', '❌ Solflare钱包未安装', 'error');
                    return;
                }

                updateResults('connection-results', '🔄 正在连接Solflare...', 'info');
                const response = await window.solflare.connect();
                
                updateResults('connection-results', 
                    `✅ Solflare连接成功！<br>地址: ${response.publicKey.toString()}`, 
                    'success'
                );
            } catch (error) {
                updateResults('connection-results', 
                    `❌ Solflare连接失败: ${error.message}`, 
                    'error'
                );
            }
        }

        async function testBackpack() {
            try {
                if (!window.backpack) {
                    updateResults('connection-results', '❌ Backpack钱包未安装', 'error');
                    return;
                }

                updateResults('connection-results', '🔄 正在连接Backpack...', 'info');
                const response = await window.backpack.connect();
                
                updateResults('connection-results', 
                    `✅ Backpack连接成功！<br>地址: ${response.publicKey.toString()}`, 
                    'success'
                );
            } catch (error) {
                updateResults('connection-results', 
                    `❌ Backpack连接失败: ${error.message}`, 
                    'error'
                );
            }
        }

        // 页面加载时自动检测
        window.addEventListener('load', () => {
            setTimeout(detectWallets, 1000);
        });
    </script>
</body>
</html>
