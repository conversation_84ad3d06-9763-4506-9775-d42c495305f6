#!/usr/bin/env python3
"""
简单的HTTP服务器，用于测试钱包连接
解决file://协议导致的钱包扩展注入问题
"""

import http.server
import socketserver
import os
import webbrowser
import threading
import time

PORT = 8000

class WalletHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def end_headers(self):
        # 添加必要的头部，确保钱包扩展可以正常工作
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', '*')
        # 确保不缓存，方便调试
        self.send_header('Cache-Control', 'no-cache, no-store, must-revalidate')
        self.send_header('Pragma', 'no-cache')
        self.send_header('Expires', '0')
        super().end_headers()

def start_server():
    """启动HTTP服务器"""
    try:
        with socketserver.TCPServer(("", PORT), WalletHTTPRequestHandler) as httpd:
            print("🚀 Bonk Game 本地服务器启动成功!")
            print(f"📍 访问地址: http://localhost:{PORT}")
            print(f"📁 服务目录: {os.getcwd()}")
            print()
            print("🔗 可用页面:")
            print(f"   主页面: http://localhost:{PORT}/index.html")
            print(f"   调试页面: http://localhost:{PORT}/debug-wallet.html")
            print(f"   简单测试: http://localhost:{PORT}/wallet-simple-test.html")
            print()
            print("💡 使用 Ctrl+C 停止服务器")
            print("🌐 正在自动打开调试页面...")
            
            # 延迟打开浏览器，确保服务器已启动
            def open_browser():
                time.sleep(1)
                webbrowser.open(f'http://localhost:{PORT}/debug-wallet.html')
            
            threading.Thread(target=open_browser, daemon=True).start()
            
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print("\n👋 服务器已停止")
    except OSError as e:
        if e.errno == 48 or "Address already in use" in str(e):
            print(f"❌ 端口 {PORT} 已被占用")
            print("💡 解决方案:")
            print("   1. 关闭其他占用该端口的程序")
            print("   2. 或者修改 PORT 变量使用其他端口")
        else:
            print(f"❌ 启动服务器失败: {e}")

if __name__ == "__main__":
    start_server()
