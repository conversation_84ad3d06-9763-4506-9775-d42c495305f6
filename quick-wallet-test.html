<!DOCTYPE html>
<html>
<head>
    <title>Quick Wallet Test</title>
    <meta charset="utf-8" />
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #1a1a1a; color: white; }
        .result { background: #2a2a2a; padding: 10px; margin: 5px 0; border-radius: 5px; }
        .detected { border-left: 4px solid #4CAF50; }
        .not-detected { border-left: 4px solid #f44336; }
        button { background: #4CAF50; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin: 5px; }
    </style>
</head>
<body>
    <h1>Quick Wallet Detection</h1>
    <button onclick="testWallets()">Test All Wallets</button>
    <div id="results"></div>

    <script>
        function testWallets() {
            const results = document.getElementById('results');
            results.innerHTML = '';
            
            const tests = [
                { name: 'Phantom (window.solana)', test: () => window.solana?.isPhantom },
                { name: 'Phantom (window.phantom)', test: () => window.phantom?.solana?.isPhantom },
                { name: 'OKX (window.okxwallet)', test: () => window.okxwallet?.solana },
                { name: 'OKX (window.okx)', test: () => window.okx?.solana },
                { name: 'Solflare (window.solflare)', test: () => window.solflare?.isSolflare },
                { name: 'Solflare (window.SolflareApp)', test: () => window.SolflareApp },
                { name: 'Backpack (window.backpack)', test: () => window.backpack?.isBackpack },
                { name: 'Backpack (window.xnft)', test: () => window.xnft?.solana }
            ];
            
            tests.forEach(test => {
                const detected = test.test();
                const div = document.createElement('div');
                div.className = `result ${detected ? 'detected' : 'not-detected'}`;
                div.textContent = `${test.name}: ${detected ? '✅ DETECTED' : '❌ NOT DETECTED'}`;
                results.appendChild(div);
            });
            
            // Show all window properties containing wallet-related keywords
            const allProps = Object.keys(window).filter(key => 
                /solana|phantom|okx|solflare|backpack|xnft/i.test(key)
            );
            
            if (allProps.length > 0) {
                const div = document.createElement('div');
                div.className = 'result';
                div.innerHTML = `<strong>Found window properties:</strong><br>${allProps.join(', ')}`;
                results.appendChild(div);
            }
        }
        
        // Auto-test on load
        setTimeout(testWallets, 1000);
    </script>
</body>
</html>
