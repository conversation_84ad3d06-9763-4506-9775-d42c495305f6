<!DOCTYPE HTML>
<html>
<head>
    <title>Phantom Wallet Direct Integration - Bonk Game</title>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #1a1a1a;
            color: white;
            padding: 2rem;
            margin: 0;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        .wallet-btn {
            background: linear-gradient(135deg, #9945FF, #14F195);
            color: white;
            border: none;
            padding: 0.8rem 1.5rem;
            border-radius: 0.5rem;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 0.5rem;
        }
        .wallet-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(153, 69, 255, 0.3);
        }
        .wallet-btn.connected {
            background: linear-gradient(135deg, #14F195, #9945FF);
        }
        .status {
            padding: 1rem;
            background: #333;
            border-radius: 0.5rem;
            margin: 1rem 0;
            border: 1px solid #555;
        }
        .status.connected {
            border-color: #14F195;
            background: rgba(20, 241, 149, 0.1);
        }
        .status.disconnected {
            border-color: #888;
        }
        .address {
            font-family: monospace;
            word-break: break-all;
            margin: 0.5rem 0;
        }
        .install-prompt {
            background: #ff4444;
            color: white;
            padding: 1rem;
            border-radius: 0.5rem;
            margin: 1rem 0;
            display: none;
        }
        .info-section {
            background: #2a2a2a;
            padding: 1.5rem;
            border-radius: 0.5rem;
            margin: 1rem 0;
        }
        .code-block {
            background: #1a1a1a;
            padding: 1rem;
            border-radius: 0.3rem;
            overflow-x: auto;
            font-family: monospace;
            font-size: 0.9rem;
            border: 1px solid #444;
        }
        .balance-info {
            background: #2a2a2a;
            padding: 1rem;
            border-radius: 0.5rem;
            margin: 1rem 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎮 Bonk Game - Phantom Wallet Direct Integration</h1>
        
        <div class="info-section">
            <h3>📋 正确的集成方法</h3>
            <p>对于静态HTML网站，我们直接使用 <code>window.solana</code> API，这是Phantom钱包官方推荐的方式。</p>
        </div>

        <div class="install-prompt" id="install-prompt">
            <h3>⚠️ 未检测到Phantom钱包</h3>
            <p>请先安装 <a href="https://phantom.app" target="_blank">Phantom钱包</a> 扩展程序。</p>
        </div>

        <div class="status" id="wallet-status">
            <h3>🔗 钱包连接状态</h3>
            <div id="status">检测中...</div>
            <div id="wallet-address" class="address"></div>
        </div>

        <div>
            <button id="phantom-connect-btn" class="wallet-btn">Connect Phantom</button>
            <button id="get-balance-btn" class="wallet-btn">Get Balance</button>
        </div>

        <div class="balance-info" id="balance-info" style="display: none;">
            <h3>💰 钱包余额</h3>
            <div id="balance-display">-</div>
        </div>

        <div class="info-section">
            <h3>🔧 技术实现</h3>
            <p>这个示例展示了如何使用vanilla JavaScript直接与Phantom钱包交互：</p>
            <div class="code-block">
// 检测Phantom钱包
const phantom = window.solana?.isPhantom ? window.solana : null;

// 连接钱包
const response = await phantom.connect();
const publicKey = response.publicKey.toString();

// 监听事件
phantom.on('connect', () => console.log('Connected'));
phantom.on('disconnect', () => console.log('Disconnected'));
            </div>
        </div>

        <div class="info-section">
            <h3>💡 关键要点</h3>
            <ul>
                <li>不需要@solana/wallet-adapter - 直接使用钱包API</li>
                <li>支持所有主流钱包的原生API</li>
                <li>适用于静态HTML网站</li>
                <li>无需构建工具或复杂依赖</li>
            </ul>
        </div>
    </div>

    <!-- Solana Web3.js for balance checking -->
    <script src="https://unpkg.com/@solana/web3.js@latest/lib/index.iife.min.js"></script>
    
    <script>
        class PhantomWalletDemo {
            constructor() {
                this.wallet = null;
                this.publicKey = null;
                this.connected = false;
                this.init();
            }

            init() {
                this.checkWalletAvailability();
                this.setupEventListeners();
            }

            checkWalletAvailability() {
                if (window.solana && window.solana.isPhantom) {
                    console.log('✅ Phantom wallet detected');
                    this.wallet = window.solana;
                    this.updateStatus('Phantom wallet detected', 'ready');
                    this.checkAutoConnect();
                } else {
                    console.log('❌ Phantom wallet not detected');
                    this.updateStatus('Phantom wallet not detected', 'error');
                    this.showInstallPrompt();
                }
            }

            async checkAutoConnect() {
                try {
                    // 尝试无提示连接（如果之前已授权）
                    if (this.wallet.isConnected) {
                        this.publicKey = this.wallet.publicKey.toString();
                        this.connected = true;
                        this.updateStatus('Auto-connected to Phantom', 'connected');
                        this.updateUI();
                    } else {
                        this.updateStatus('Ready to connect', 'ready');
                    }
                } catch (error) {
                    console.log('No auto-connect available');
                    this.updateStatus('Ready to connect', 'ready');
                }
            }

            async connectWallet() {
                if (!this.wallet) {
                    alert('请先安装Phantom钱包扩展程序');
                    return;
                }

                try {
                    this.updateStatus('Connecting...', 'connecting');
                    
                    // 请求连接权限
                    const response = await this.wallet.connect();
                    this.publicKey = response.publicKey.toString();
                    this.connected = true;
                    
                    this.updateStatus('Connected to Phantom', 'connected');
                    this.updateUI();
                    
                    console.log('✅ Connected to Phantom:', this.publicKey);
                    
                } catch (error) {
                    console.error('❌ Connection failed:', error);
                    this.updateStatus('Connection failed: ' + error.message, 'error');
                }
            }

            async disconnectWallet() {
                if (!this.wallet || !this.connected) return;

                try {
                    await this.wallet.disconnect();
                    this.publicKey = null;
                    this.connected = false;
                    
                    this.updateStatus('Disconnected from Phantom', 'ready');
                    this.updateUI();
                    
                    console.log('✅ Disconnected from Phantom');
                    
                } catch (error) {
                    console.error('❌ Disconnect failed:', error);
                }
            }

            async getBalance() {
                if (!this.connected) {
                    alert('请先连接钱包');
                    return;
                }

                try {
                    // 连接到Solana主网
                    const connection = new solanaWeb3.Connection(
                        'https://api.mainnet-beta.solana.com',
                        'confirmed'
                    );
                    
                    const balance = await connection.getBalance(
                        new solanaWeb3.PublicKey(this.publicKey)
                    );
                    
                    const solBalance = balance / solanaWeb3.LAMPORTS_PER_SOL;
                    
                    const balanceDisplay = document.getElementById('balance-display');
                    const balanceInfo = document.getElementById('balance-info');
                    
                    balanceDisplay.textContent = `${solBalance.toFixed(4)} SOL`;
                    balanceInfo.style.display = 'block';
                    
                    console.log('💰 Balance:', solBalance, 'SOL');
                    
                } catch (error) {
                    console.error('❌ Failed to get balance:', error);
                    alert('获取余额失败: ' + error.message);
                }
            }

            setupEventListeners() {
                const connectBtn = document.getElementById('phantom-connect-btn');
                const balanceBtn = document.getElementById('get-balance-btn');

                connectBtn.addEventListener('click', () => {
                    if (this.connected) {
                        this.disconnectWallet();
                    } else {
                        this.connectWallet();
                    }
                });

                balanceBtn.addEventListener('click', () => {
                    this.getBalance();
                });

                // 监听钱包事件
                if (this.wallet) {
                    this.wallet.on('connect', () => {
                        console.log('🔗 Wallet connected event');
                        this.connected = true;
                        this.updateUI();
                    });

                    this.wallet.on('disconnect', () => {
                        console.log('🔌 Wallet disconnected event');
                        this.connected = false;
                        this.publicKey = null;
                        this.updateUI();
                    });

                    this.wallet.on('accountChanged', (publicKey) => {
                        console.log('👤 Account changed:', publicKey?.toString());
                        if (publicKey) {
                            this.publicKey = publicKey.toString();
                            this.connected = true;
                        } else {
                            this.publicKey = null;
                            this.connected = false;
                        }
                        this.updateUI();
                    });
                }
            }

            updateStatus(message, type = 'info') {
                const statusDiv = document.getElementById('status');
                const statusContainer = document.getElementById('wallet-status');
                
                statusDiv.textContent = message;
                statusContainer.className = `status ${type}`;
                
                // 更新地址显示
                const addressDiv = document.getElementById('wallet-address');
                if (this.connected && this.publicKey) {
                    addressDiv.textContent = `Address: ${this.publicKey}`;
                } else {
                    addressDiv.textContent = '';
                }
            }

            updateUI() {
                const connectBtn = document.getElementById('phantom-connect-btn');
                
                if (this.connected) {
                    connectBtn.textContent = 'Disconnect Phantom';
                    connectBtn.classList.add('connected');
                    this.updateStatus('Connected to Phantom', 'connected');
                } else {
                    connectBtn.textContent = 'Connect Phantom';
                    connectBtn.classList.remove('connected');
                    this.updateStatus('Ready to connect', 'ready');
                }
            }

            showInstallPrompt() {
                const installPrompt = document.getElementById('install-prompt');
                installPrompt.style.display = 'block';
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            window.phantomDemo = new PhantomWalletDemo();
        });
    </script>
</body>
</html>