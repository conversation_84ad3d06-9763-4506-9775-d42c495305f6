/**
 * 简化的Solana钱包连接器
 * 使用钱包原生API，支持主流钱包
 */

class SimpleWalletConnector {
    constructor() {
        this.wallet = null;
        this.publicKey = null;
        this.connected = false;
        this.walletType = null;

        // 只支持主流钱包，使用最简单的检测方法
        this.supportedWallets = [
            {
                name: 'Phantom',
                key: 'phantom',
                icon: '👻',
                getProvider: () => window.solana?.isPhantom ? window.solana : null,
                downloadUrl: 'https://phantom.app'
            },
            {
                name: 'Solflare',
                key: 'solflare',
                icon: '🔥',
                getProvider: () => window.solflare?.isSolflare ? window.solflare : null,
                downloadUrl: 'https://solflare.com'
            },
            {
                name: 'Backpack',
                key: 'backpack',
                icon: '🎒',
                getProvider: () => window.backpack ? window.backpack : null,
                downloadUrl: 'https://backpack.app'
            }
        ];

        this.init();
    }

    init() {
        this.setupUI();
        // 延迟检测钱包，给钱包注入时间
        setTimeout(() => {
            this.debugWalletDetection();
            this.checkPreviousConnection();
        }, 1000);
    }

    debugWalletDetection() {
        console.log('=== 钱包检测调试信息 ===');
        console.log('window.solana:', window.solana);
        console.log('window.solflare:', window.solflare);
        console.log('window.slope:', window.slope);
        console.log('window.coin98:', window.coin98);
        console.log('window.glow:', window.glow);
        console.log('所有window属性:', Object.keys(window).filter(key =>
            key.toLowerCase().includes('solana') ||
            key.toLowerCase().includes('phantom') ||
            key.toLowerCase().includes('solflare') ||
            key.toLowerCase().includes('slope') ||
            key.toLowerCase().includes('coin98') ||
            key.toLowerCase().includes('glow')
        ));
        console.log('========================');
    }

    setupUI() {
        const walletButton = document.getElementById('wallet-connect-btn');
        const walletModal = document.getElementById('wallet-modal');
        const closeModal = document.getElementById('close-wallet-modal');
        const walletList = document.getElementById('wallet-list');

        if (walletButton) {
            walletButton.addEventListener('click', () => {
                if (this.connected) {
                    this.disconnect();
                } else {
                    this.showWalletModal();
                }
            });
        }

        if (closeModal) {
            closeModal.addEventListener('click', () => {
                this.hideWalletModal();
            });
        }

        if (walletModal) {
            walletModal.addEventListener('click', (e) => {
                if (e.target === walletModal) {
                    this.hideWalletModal();
                }
            });
        }

        this.populateWalletList();
    }

    populateWalletList() {
        const walletList = document.getElementById('wallet-list');
        if (!walletList) return;

        walletList.innerHTML = '';

        // 添加刷新按钮
        const refreshButton = document.createElement('div');
        refreshButton.className = 'wallet-item wallet-refresh';
        refreshButton.innerHTML = `
            <div class="wallet-info">
                <span class="wallet-name">🔄 刷新钱包检测</span>
                <span class="wallet-status">点击重新检测钱包</span>
            </div>
        `;
        refreshButton.style.cursor = 'pointer';
        refreshButton.addEventListener('click', () => {
            this.debugWalletDetection();
            this.populateWalletList();
        });
        walletList.appendChild(refreshButton);

        this.supportedWallets.forEach(wallet => {
            const isInstalled = this.isWalletInstalled(wallet.key);
            const walletItem = document.createElement('div');
            walletItem.className = `wallet-item ${!isInstalled ? 'wallet-not-installed' : ''}`;
            walletItem.innerHTML = `
                <div class="wallet-info">
                    <span class="wallet-name">${wallet.name}</span>
                    <span class="wallet-status">${isInstalled ? 'Installed' : 'Not Installed'}</span>
                </div>
            `;

            if (isInstalled) {
                walletItem.style.cursor = 'pointer';
                walletItem.addEventListener('click', () => {
                    this.connectWallet(wallet.key);
                });
            } else {
                walletItem.addEventListener('click', () => {
                    this.redirectToWallet(wallet.key);
                });
            }

            walletList.appendChild(walletItem);
        });
    }

    isWalletInstalled(walletKey) {
        const wallet = this.supportedWallets.find(w => w.key === walletKey);
        if (!wallet) return false;

        const isInstalled = wallet.detector();
        console.log(`${wallet.name} 检测结果:`, isInstalled);
        return isInstalled;
    }

    getWalletObject(walletKey) {
        switch (walletKey) {
            case 'phantom':
                return window.solana?.isPhantom ? window.solana : null;
            case 'solflare':
                return window.solflare?.isSolflare ? window.solflare : null;
            case 'slope':
                return window.slope;
            case 'coinbaseWallet':
                return window.solana?.isCoinbaseWallet ? window.solana : null;
            case 'trustWallet':
                return window.solana?.isTrustWallet ? window.solana : null;
            case 'coin98':
                return window.coin98?.solana;
            case 'glow':
                return window.glow;
            case 'backpack':
                return window.backpack;
            default:
                return null;
        }
    }

    async connectWallet(walletKey) {
        try {
            const walletObj = this.getWalletObject(walletKey);
            if (!walletObj) {
                throw new Error('Wallet not found');
            }

            const response = await walletObj.connect();
            this.wallet = walletObj;
            this.publicKey = response.publicKey.toString();
            this.connected = true;

            this.saveConnection(walletKey);
            this.updateUI();
            this.hideWalletModal();

            console.log('Connected to wallet:', this.publicKey);
        } catch (error) {
            console.error('Failed to connect wallet:', error);
            alert('Failed to connect wallet. Please try again.');
        }
    }

    async disconnect() {
        try {
            if (this.wallet && this.wallet.disconnect) {
                await this.wallet.disconnect();
            }
            
            this.wallet = null;
            this.publicKey = null;
            this.connected = false;
            
            this.clearConnection();
            this.updateUI();

            console.log('Disconnected from wallet');
        } catch (error) {
            console.error('Failed to disconnect wallet:', error);
        }
    }

    showWalletModal() {
        const modal = document.getElementById('wallet-modal');
        if (modal) {
            modal.style.display = 'flex';
            this.populateWalletList();
        }
    }

    hideWalletModal() {
        const modal = document.getElementById('wallet-modal');
        if (modal) {
            modal.style.display = 'none';
        }
    }

    updateUI() {
        const button = document.getElementById('wallet-connect-btn');
        if (!button) return;

        if (this.connected) {
            const shortAddress = `${this.publicKey.slice(0, 4)}...${this.publicKey.slice(-4)}`;
            button.innerHTML = `<i class="fas fa-wallet"></i> ${shortAddress}`;
            button.classList.add('connected');
        } else {
            button.innerHTML = '<i class="fas fa-wallet"></i> Connect Wallet';
            button.classList.remove('connected');
        }
    }

    saveConnection(walletKey) {
        localStorage.setItem('solana_wallet_connected', 'true');
        localStorage.setItem('solana_wallet_type', walletKey);
        localStorage.setItem('solana_wallet_address', this.publicKey);
    }

    clearConnection() {
        localStorage.removeItem('solana_wallet_connected');
        localStorage.removeItem('solana_wallet_type');
        localStorage.removeItem('solana_wallet_address');
    }

    checkPreviousConnection() {
        const wasConnected = localStorage.getItem('solana_wallet_connected');
        const walletType = localStorage.getItem('solana_wallet_type');
        const savedAddress = localStorage.getItem('solana_wallet_address');

        if (wasConnected && walletType && savedAddress) {
            if (this.isWalletInstalled(walletType)) {
                this.autoReconnect(walletType);
            }
        }
    }

    async autoReconnect(walletKey) {
        try {
            const walletObj = this.getWalletObject(walletKey);
            if (!walletObj) return;

            // Try to connect silently (only if wallet allows it)
            if (walletObj.isConnected) {
                this.wallet = walletObj;
                this.publicKey = walletObj.publicKey.toString();
                this.connected = true;
                this.updateUI();
            }
        } catch (error) {
            // Silent failure for auto-reconnect
            this.clearConnection();
        }
    }

    redirectToWallet(walletKey) {
        const walletUrls = {
            phantom: 'https://phantom.app',
            solflare: 'https://solflare.com',
            slope: 'https://slope.finance',
            coinbaseWallet: 'https://www.coinbase.com/wallet',
            trustWallet: 'https://trustwallet.com',
            coin98: 'https://coin98.com',
            glow: 'https://glow.app',
            mathWallet: 'https://mathwallet.org'
        };

        const url = walletUrls[walletKey];
        if (url) {
            window.open(url, '_blank');
        }
    }
}

// Initialize wallet connector when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.solanaWalletConnector = new SolanaWalletConnector();
});