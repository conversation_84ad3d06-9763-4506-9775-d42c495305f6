/**
 * 简化的Solana钱包连接器
 * 使用钱包原生API，支持主流钱包
 */

class BonkWalletConnector {
    constructor() {
        this.wallet = null;
        this.publicKey = null;
        this.connected = false;
        this.walletType = null;

        // 只支持主流钱包，使用最简单的检测方法
        this.supportedWallets = [
            {
                name: 'Phantom',
                key: 'phantom',
                icon: '👻',
                getProvider: () => window.solana?.isPhantom ? window.solana : null,
                downloadUrl: 'https://phantom.app'
            },
            {
                name: 'Solflare',
                key: 'solflare',
                icon: '🔥',
                getProvider: () => window.solflare?.isSolflare ? window.solflare : null,
                downloadUrl: 'https://solflare.com'
            },
            {
                name: 'Backpack',
                key: 'backpack',
                icon: '🎒',
                getProvider: () => window.backpack ? window.backpack : null,
                downloadUrl: 'https://backpack.app'
            }
        ];

        this.init();
    }

    init() {
        this.setupUI();
        // 延迟检测钱包，给钱包注入时间
        setTimeout(() => {
            this.debugWalletDetection();
            this.checkPreviousConnection();
        }, 1000);
    }

    debugWalletDetection() {
        console.log('=== 钱包检测调试信息 ===');
        console.log('window.solana:', window.solana);
        console.log('window.solflare:', window.solflare);
        console.log('window.slope:', window.slope);
        console.log('window.coin98:', window.coin98);
        console.log('window.glow:', window.glow);
        console.log('所有window属性:', Object.keys(window).filter(key =>
            key.toLowerCase().includes('solana') ||
            key.toLowerCase().includes('phantom') ||
            key.toLowerCase().includes('solflare') ||
            key.toLowerCase().includes('slope') ||
            key.toLowerCase().includes('coin98') ||
            key.toLowerCase().includes('glow')
        ));
        console.log('========================');
    }

    setupUI() {
        const walletButton = document.getElementById('wallet-connect-btn');
        const walletModal = document.getElementById('wallet-modal');
        const closeModal = document.getElementById('close-wallet-modal');
        const walletList = document.getElementById('wallet-list');

        if (walletButton) {
            walletButton.addEventListener('click', (e) => {
                e.stopPropagation();
                if (this.connected) {
                    this.toggleDropdown();
                } else {
                    this.showWalletModal();
                }
            });
        }

        if (closeModal) {
            closeModal.addEventListener('click', () => {
                this.hideWalletModal();
            });
        }

        if (walletModal) {
            walletModal.addEventListener('click', (e) => {
                if (e.target === walletModal) {
                    this.hideWalletModal();
                }
            });
        }

        // 设置下拉菜单事件
        this.setupDropdownEvents();

        // 点击外部关闭下拉菜单
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.wallet-container')) {
                this.hideDropdown();
            }
        });

        this.populateWalletList();
    }

    setupDropdownEvents() {
        // 复制地址
        const copyAddressBtn = document.getElementById('copy-address-btn');
        if (copyAddressBtn) {
            copyAddressBtn.addEventListener('click', () => {
                this.copyAddress();
            });
        }

        // 在浏览器中查看
        const viewExplorerBtn = document.getElementById('view-explorer-btn');
        if (viewExplorerBtn) {
            viewExplorerBtn.addEventListener('click', () => {
                this.viewInExplorer();
            });
        }

        // 刷新余额
        const refreshBalanceBtn = document.getElementById('refresh-balance-btn');
        if (refreshBalanceBtn) {
            refreshBalanceBtn.addEventListener('click', () => {
                this.refreshBalance();
            });
        }

        // 断开连接
        const disconnectBtn = document.getElementById('disconnect-wallet-btn');
        if (disconnectBtn) {
            disconnectBtn.addEventListener('click', () => {
                this.disconnect();
                this.hideDropdown();
            });
        }
    }

    toggleDropdown() {
        const dropdown = document.getElementById('wallet-dropdown');
        if (dropdown) {
            dropdown.classList.toggle('show');
        }
    }

    hideDropdown() {
        const dropdown = document.getElementById('wallet-dropdown');
        if (dropdown) {
            dropdown.classList.remove('show');
        }
    }

    async copyAddress() {
        if (this.publicKey) {
            try {
                await navigator.clipboard.writeText(this.publicKey);
                this.showNotification('地址已复制到剪贴板', 'success');
            } catch (error) {
                // 备用方法
                const textArea = document.createElement('textarea');
                textArea.value = this.publicKey;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                this.showNotification('地址已复制到剪贴板', 'success');
            }
        }
    }

    viewInExplorer() {
        if (this.publicKey) {
            const url = `https://explorer.solana.com/address/${this.publicKey}`;
            window.open(url, '_blank');
        }
    }

    async refreshBalance() {
        if (!this.publicKey) return;

        const balanceDisplay = document.getElementById('wallet-balance-display');
        if (balanceDisplay) {
            balanceDisplay.textContent = '余额: 刷新中...';
        }

        try {
            // 这里可以添加实际的余额查询逻辑
            // 暂时显示模拟数据
            setTimeout(() => {
                if (balanceDisplay) {
                    balanceDisplay.textContent = '余额: -- SOL';
                }
                this.showNotification('余额已刷新', 'info');
            }, 1000);
        } catch (error) {
            console.error('刷新余额失败:', error);
            if (balanceDisplay) {
                balanceDisplay.textContent = '余额: 获取失败';
            }
        }
    }

    showNotification(message, type = 'info') {
        // 创建通知元素
        const notification = document.createElement('div');
        notification.className = `wallet-notification ${type}`;
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${type === 'success' ? '#14F195' : type === 'error' ? '#ff6b6b' : '#9945FF'};
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            z-index: 10000;
            font-size: 14px;
            font-weight: 600;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            transform: translateX(100%);
            transition: transform 0.3s ease;
        `;

        document.body.appendChild(notification);

        // 显示动画
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);

        // 自动隐藏
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }

    populateWalletList() {
        const walletList = document.getElementById('wallet-list');
        if (!walletList) return;

        walletList.innerHTML = '';

        // 添加刷新按钮
        const refreshButton = document.createElement('div');
        refreshButton.className = 'wallet-item wallet-refresh';
        refreshButton.innerHTML = `
            <div class="wallet-icon">🔄</div>
            <div class="wallet-info">
                <span class="wallet-name">刷新钱包检测</span>
                <span class="wallet-status">点击重新检测已安装的钱包</span>
            </div>
        `;
        refreshButton.style.cursor = 'pointer';
        refreshButton.addEventListener('click', () => {
            this.debugWalletDetection();
            this.populateWalletList();
        });
        walletList.appendChild(refreshButton);

        this.supportedWallets.forEach(wallet => {
            const provider = wallet.getProvider();
            const isInstalled = provider !== null;
            const walletItem = document.createElement('div');
            walletItem.className = `wallet-item ${!isInstalled ? 'wallet-not-installed' : ''}`;
            walletItem.innerHTML = `
                <div class="wallet-icon">${wallet.icon}</div>
                <div class="wallet-info">
                    <span class="wallet-name">${wallet.name}</span>
                    <span class="wallet-status">${isInstalled ? '✅ 已安装，点击连接' : '❌ 未安装，点击下载'}</span>
                </div>
            `;

            if (isInstalled) {
                walletItem.style.cursor = 'pointer';
                walletItem.addEventListener('click', () => {
                    this.connectWallet(wallet.key);
                });
            } else {
                walletItem.addEventListener('click', () => {
                    window.open(wallet.downloadUrl, '_blank');
                });
            }

            walletList.appendChild(walletItem);
        });
    }

    getWalletProvider(walletKey) {
        const wallet = this.supportedWallets.find(w => w.key === walletKey);
        if (!wallet) return null;

        const provider = wallet.getProvider();
        console.log(`${wallet.name} 提供者:`, provider);
        return provider;
    }

    async connectWallet(walletKey) {
        try {
            const provider = this.getWalletProvider(walletKey);
            if (!provider) {
                throw new Error(`${walletKey} wallet not found or not installed`);
            }

            console.log(`正在连接 ${walletKey} 钱包...`);
            const response = await provider.connect();

            this.wallet = provider;
            this.walletType = walletKey;
            this.publicKey = response.publicKey.toString();
            this.connected = true;

            this.saveConnection(walletKey);
            this.updateUI();
            this.hideWalletModal();

            console.log(`✅ 成功连接到 ${walletKey}:`, this.publicKey);
        } catch (error) {
            console.error(`❌ 连接 ${walletKey} 失败:`, error);
            alert(`连接 ${walletKey} 钱包失败: ${error.message}`);
        }
    }

    async disconnect() {
        try {
            if (this.wallet && this.wallet.disconnect) {
                await this.wallet.disconnect();
            }

            const walletType = this.walletType;
            this.wallet = null;
            this.walletType = null;
            this.publicKey = null;
            this.connected = false;

            this.clearConnection();
            this.updateUI();

            console.log(`✅ 已断开 ${walletType} 钱包连接`);
        } catch (error) {
            console.error('❌ 断开钱包连接失败:', error);
        }
    }

    showWalletModal() {
        const modal = document.getElementById('wallet-modal');
        if (modal) {
            modal.style.display = 'flex';
            this.populateWalletList();
        }
    }

    hideWalletModal() {
        const modal = document.getElementById('wallet-modal');
        if (modal) {
            modal.style.display = 'none';
        }
    }

    updateUI() {
        const button = document.getElementById('wallet-connect-btn');
        const addressDisplay = document.getElementById('wallet-address-display');
        const balanceDisplay = document.getElementById('wallet-balance-display');

        if (!button) return;

        if (this.connected) {
            const shortAddress = `${this.publicKey.slice(0, 4)}...${this.publicKey.slice(-4)}`;
            button.innerHTML = `<i class="fas fa-wallet"></i> ${shortAddress} <i class="fas fa-chevron-down"></i>`;
            button.classList.add('connected');

            // 更新下拉菜单中的地址显示
            if (addressDisplay) {
                addressDisplay.textContent = this.publicKey;
            }

            // 初始化余额显示
            if (balanceDisplay) {
                balanceDisplay.textContent = '余额: -- SOL';
                // 可以在这里添加实际的余额查询
                this.refreshBalance();
            }
        } else {
            button.innerHTML = '<i class="fas fa-wallet"></i> Connect Wallet';
            button.classList.remove('connected');
            this.hideDropdown();
        }
    }

    saveConnection(walletKey) {
        localStorage.setItem('solana_wallet_connected', 'true');
        localStorage.setItem('solana_wallet_type', walletKey);
        localStorage.setItem('solana_wallet_address', this.publicKey);
    }

    clearConnection() {
        localStorage.removeItem('solana_wallet_connected');
        localStorage.removeItem('solana_wallet_type');
        localStorage.removeItem('solana_wallet_address');
    }

    checkPreviousConnection() {
        const wasConnected = localStorage.getItem('solana_wallet_connected');
        const walletType = localStorage.getItem('solana_wallet_type');
        const savedAddress = localStorage.getItem('solana_wallet_address');

        if (wasConnected && walletType && savedAddress) {
            const provider = this.getWalletProvider(walletType);
            if (provider) {
                this.autoReconnect(walletType, provider);
            }
        }
    }

    async autoReconnect(walletKey, provider) {
        try {
            // 尝试静默重连（仅当钱包允许时）
            if (provider.isConnected) {
                this.wallet = provider;
                this.walletType = walletKey;
                this.publicKey = provider.publicKey.toString();
                this.connected = true;
                this.updateUI();
                console.log(`🔄 自动重连到 ${walletKey}:`, this.publicKey);
            }
        } catch (error) {
            // 自动重连失败时静默处理
            console.log(`自动重连 ${walletKey} 失败，清除保存的连接状态`);
            this.clearConnection();
        }
    }

}

// 初始化钱包连接器
document.addEventListener('DOMContentLoaded', () => {
    window.bonkWalletConnector = new BonkWalletConnector();
    console.log('🚀 Bonk Game 钱包连接器已初始化');
});