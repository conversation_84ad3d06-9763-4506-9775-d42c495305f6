/**
 * Simplified Solana Wallet Connector
 * Uses native wallet APIs, supports mainstream wallets
 */

class BonkWalletConnector {
    constructor() {
        this.wallet = null;
        this.publicKey = null;
        this.connected = false;
        this.walletType = null;

        // Only support mainstream wallets, using the simplest detection method
        this.supportedWallets = [
            {
                name: 'Phantom',
                key: 'phantom',
                icon: '👻',
                getProvider: () => {
                    if (window.solana?.isPhantom) return window.solana;
                    if (window.phantom?.solana?.isPhantom) return window.phantom.solana;
                    return null;
                },
                downloadUrl: 'https://phantom.app'
            },
            {
                name: 'OKX Wallet',
                key: 'okx',
                icon: '⭕',
                getProvider: () => {
                    if (window.okxwallet?.solana) return window.okxwallet.solana;
                    if (window.okx?.solana) return window.okx.solana;
                    return null;
                },
                downloadUrl: 'https://www.okx.com/web3'
            },
            {
                name: '<PERSON><PERSON>lar<PERSON>',
                key: 'solflare',
                icon: '🔥',
                getProvider: () => {
                    if (window.solflare?.isSolflare) return window.solflare;
                    if (window.SolflareApp) return window.SolflareApp;
                    return null;
                },
                downloadUrl: 'https://solflare.com'
            },
            {
                name: 'Backpack',
                key: 'backpack',
                icon: '🎒',
                getProvider: () => {
                    if (window.backpack?.isBackpack) return window.backpack;
                    if (window.xnft?.solana) return window.xnft.solana;
                    return null;
                },
                downloadUrl: 'https://backpack.app'
            }
        ];

        this.init();
    }

    init() {
        this.setupUI();
        // Delay wallet detection to give wallets time to inject
        setTimeout(() => {
            this.checkPreviousConnection();
        }, 1000);
    }

    setupUI() {
        const walletButton = document.getElementById('wallet-connect-btn');
        const walletModal = document.getElementById('wallet-modal');
        const closeModal = document.getElementById('close-wallet-modal');

        if (walletButton) {
            walletButton.addEventListener('click', (e) => {
                e.stopPropagation();
                if (this.connected) {
                    this.toggleDropdown();
                } else {
                    this.showWalletModal();
                }
            });
        }

        if (closeModal) {
            closeModal.addEventListener('click', () => {
                this.hideWalletModal();
            });
        }

        if (walletModal) {
            walletModal.addEventListener('click', (e) => {
                if (e.target === walletModal) {
                    this.hideWalletModal();
                }
            });
        }

        // Setup dropdown menu events
        this.setupDropdownEvents();

        // Click outside to close dropdown menu
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.wallet-container')) {
                this.hideDropdown();
            }
        });

        this.populateWalletList();
    }

    setupDropdownEvents() {
        // Copy address
        const copyAddressBtn = document.getElementById('copy-address-btn');
        if (copyAddressBtn) {
            copyAddressBtn.addEventListener('click', () => {
                this.copyAddress();
            });
        }

        // View in explorer
        const viewExplorerBtn = document.getElementById('view-explorer-btn');
        if (viewExplorerBtn) {
            viewExplorerBtn.addEventListener('click', () => {
                this.viewInExplorer();
            });
        }

        // Refresh balance
        const refreshBalanceBtn = document.getElementById('refresh-balance-btn');
        if (refreshBalanceBtn) {
            refreshBalanceBtn.addEventListener('click', () => {
                this.refreshBalance();
            });
        }

        // Disconnect wallet
        const disconnectBtn = document.getElementById('disconnect-wallet-btn');
        if (disconnectBtn) {
            disconnectBtn.addEventListener('click', () => {
                this.disconnect();
                this.hideDropdown();
            });
        }
    }

    toggleDropdown() {
        const dropdown = document.getElementById('wallet-dropdown');
        if (dropdown) {
            dropdown.classList.toggle('show');
        }
    }

    hideDropdown() {
        const dropdown = document.getElementById('wallet-dropdown');
        if (dropdown) {
            dropdown.classList.remove('show');
        }
    }

    async copyAddress() {
        if (this.publicKey) {
            try {
                await navigator.clipboard.writeText(this.publicKey);
                this.showNotification('Address copied to clipboard', 'success');
            } catch (error) {
                // Fallback method
                const textArea = document.createElement('textarea');
                textArea.value = this.publicKey;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                this.showNotification('Address copied to clipboard', 'success');
            }
        }
    }

    viewInExplorer() {
        if (this.publicKey) {
            const url = `https://explorer.solana.com/address/${this.publicKey}`;
            window.open(url, '_blank');
        }
    }

    async refreshBalance() {
        if (!this.publicKey) return;

        const balanceDisplay = document.getElementById('wallet-balance-display');
        if (balanceDisplay) {
            balanceDisplay.textContent = 'Balance: Refreshing...';
        }

        try {
            // Here you can add actual balance query logic
            // Currently showing mock data
            setTimeout(() => {
                if (balanceDisplay) {
                    balanceDisplay.textContent = 'Balance: -- SOL';
                }
                this.showNotification('Balance refreshed', 'info');
            }, 1000);
        } catch (error) {
            // Silent handling when balance refresh fails
            if (balanceDisplay) {
                balanceDisplay.textContent = 'Balance: Failed to load';
            }
        }
    }

    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `wallet-notification ${type}`;
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${type === 'success' ? '#14F195' : type === 'error' ? '#ff6b6b' : '#9945FF'};
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            z-index: 10000;
            font-size: 14px;
            font-weight: 600;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            transform: translateX(100%);
            transition: transform 0.3s ease;
        `;

        document.body.appendChild(notification);

        // Show animation
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);

        // Auto hide
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }

    populateWalletList() {
        const walletList = document.getElementById('wallet-list');
        if (!walletList) return;

        walletList.innerHTML = '';

        // Get only installed wallets
        const installedWallets = this.supportedWallets.filter(wallet => {
            const provider = wallet.getProvider();
            return provider !== null;
        });

        if (installedWallets.length > 0) {
            // Show installed wallets
            installedWallets.forEach(wallet => {
                const walletItem = document.createElement('div');
                walletItem.className = 'wallet-item';
                walletItem.innerHTML = `
                    <div class="wallet-icon">${wallet.icon}</div>
                    <div class="wallet-info">
                        <span class="wallet-name">${wallet.name}</span>
                        <span class="wallet-status">Click to connect</span>
                    </div>
                `;

                walletItem.style.cursor = 'pointer';
                walletItem.addEventListener('click', () => {
                    this.connectWallet(wallet.key);
                });

                walletList.appendChild(walletItem);
            });
        } else {
            // No wallets installed - show installation guide
            this.showInstallationGuide(walletList);
        }
    }

    showInstallationGuide(walletList) {
        const guideDiv = document.createElement('div');
        guideDiv.className = 'wallet-installation-guide';
        guideDiv.innerHTML = `
            <div style="text-align: center; padding: 1rem 0 1.5rem 0; color: #888;">
                <h3 style="color: #fff; margin-bottom: 1rem; font-size: 1.1rem;">No Compatible Wallets Found</h3>
                <p style="margin-bottom: 0; font-size: 0.9rem; line-height: 1.4;">Please install one of the following wallets to continue:</p>
            </div>
        `;

        this.supportedWallets.forEach(wallet => {
            const walletItem = document.createElement('div');
            walletItem.className = 'wallet-item wallet-not-installed';
            walletItem.innerHTML = `
                <div class="wallet-icon">${wallet.icon}</div>
                <div class="wallet-info">
                    <span class="wallet-name">${wallet.name}</span>
                    <span class="wallet-status">Click to install</span>
                </div>
                <div style="color: #9bf1ff; font-size: 0.8rem;">↗</div>
            `;

            walletItem.style.cursor = 'pointer';
            walletItem.addEventListener('click', () => {
                window.open(wallet.downloadUrl, '_blank');
            });

            guideDiv.appendChild(walletItem);
        });

        walletList.appendChild(guideDiv);
    }

    getWalletProvider(walletKey) {
        const wallet = this.supportedWallets.find(w => w.key === walletKey);
        if (!wallet) return null;

        const provider = wallet.getProvider();
        return provider;
    }

    async connectWallet(walletKey) {
        try {
            const provider = this.getWalletProvider(walletKey);
            if (!provider) {
                throw new Error(`${walletKey} wallet not found or not installed`);
            }

            const response = await provider.connect();

            this.wallet = provider;
            this.walletType = walletKey;

            // Handle different response formats from different wallets
            if (response.publicKey) {
                this.publicKey = response.publicKey.toString();
            } else if (provider.publicKey) {
                this.publicKey = provider.publicKey.toString();
            } else {
                throw new Error('Unable to get public key from wallet');
            }

            this.connected = true;

            this.saveConnection(walletKey);
            this.updateUI();
            this.hideWalletModal();
        } catch (error) {
            // Silent handling when connection fails
            alert(`Failed to connect ${walletKey} wallet: ${error.message}`);
        }
    }

    async disconnect() {
        try {
            if (this.wallet && this.wallet.disconnect) {
                await this.wallet.disconnect();
            }

            this.wallet = null;
            this.walletType = null;
            this.publicKey = null;
            this.connected = false;

            this.clearConnection();
            this.updateUI();
        } catch (error) {
            // Silent handling when disconnection fails
        }
    }

    showWalletModal() {
        const modal = document.getElementById('wallet-modal');
        if (modal) {
            modal.style.display = 'flex';
            this.updateModalTitle();
            this.populateWalletList();
        }
    }

    updateModalTitle() {
        const titleElement = document.querySelector('.wallet-modal-title');
        if (!titleElement) return;

        const installedWallets = this.supportedWallets.filter(wallet => {
            const provider = wallet.getProvider();
            return provider !== null;
        });

        if (installedWallets.length > 0) {
            titleElement.textContent = 'Connect Wallet';
        } else {
            titleElement.textContent = 'Install Wallet';
        }
    }

    hideWalletModal() {
        const modal = document.getElementById('wallet-modal');
        if (modal) {
            modal.style.display = 'none';
        }
    }

    updateUI() {
        const button = document.getElementById('wallet-connect-btn');
        const addressDisplay = document.getElementById('wallet-address-display');
        const balanceDisplay = document.getElementById('wallet-balance-display');

        if (!button) return;

        if (this.connected) {
            const shortAddress = `${this.publicKey.slice(0, 4)}...${this.publicKey.slice(-4)}`;
            button.innerHTML = `<i class="fas fa-wallet"></i> ${shortAddress} <i class="fas fa-chevron-down"></i>`;
            button.classList.add('connected');

            // Update address display in dropdown menu
            if (addressDisplay) {
                addressDisplay.textContent = this.publicKey;
            }

            // Initialize balance display
            if (balanceDisplay) {
                balanceDisplay.textContent = 'Balance: -- SOL';
                // You can add actual balance query here
                this.refreshBalance();
            }
        } else {
            button.innerHTML = '<i class="fas fa-wallet"></i> Connect Wallet';
            button.classList.remove('connected');
            this.hideDropdown();
        }
    }

    saveConnection(walletKey) {
        localStorage.setItem('solana_wallet_connected', 'true');
        localStorage.setItem('solana_wallet_type', walletKey);
        localStorage.setItem('solana_wallet_address', this.publicKey);
    }

    clearConnection() {
        localStorage.removeItem('solana_wallet_connected');
        localStorage.removeItem('solana_wallet_type');
        localStorage.removeItem('solana_wallet_address');
    }

    checkPreviousConnection() {
        const wasConnected = localStorage.getItem('solana_wallet_connected');
        const walletType = localStorage.getItem('solana_wallet_type');
        const savedAddress = localStorage.getItem('solana_wallet_address');

        if (wasConnected && walletType && savedAddress) {
            const provider = this.getWalletProvider(walletType);
            if (provider) {
                this.autoReconnect(walletType, provider);
            }
        }
    }

    async autoReconnect(walletKey, provider) {
        try {
            // Attempt silent reconnection (only when wallet allows)
            if (provider.isConnected && provider.publicKey) {
                this.wallet = provider;
                this.walletType = walletKey;
                this.publicKey = provider.publicKey.toString();
                this.connected = true;
                this.updateUI();
            } else {
                // Try to connect if wallet is available but not connected
                const response = await provider.connect();
                if (response.publicKey || provider.publicKey) {
                    this.wallet = provider;
                    this.walletType = walletKey;
                    this.publicKey = (response.publicKey || provider.publicKey).toString();
                    this.connected = true;
                    this.updateUI();
                }
            }
        } catch (error) {
            // Silent handling when auto-reconnection fails
            this.clearConnection();
        }
    }

}

// Initialize wallet connector
document.addEventListener('DOMContentLoaded', () => {
    window.bonkWalletConnector = new BonkWalletConnector();
});