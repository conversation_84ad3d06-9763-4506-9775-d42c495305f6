/**
 * Solana Wallet Adapter for Bonk Game
 * Vanilla JavaScript implementation for connecting to Solana wallets
 */

class SolanaWalletAdapter {
    constructor() {
        this.wallet = null;
        this.publicKey = null;
        this.connected = false;
        this.supportedWallets = [
            { name: 'Phantom', key: 'phantom' },
            { name: 'Sol<PERSON>lar<PERSON>', key: 'solflare' },
            { name: 'Slope', key: 'slope' },
            { name: 'Coinbase Wallet', key: 'coinbaseWallet' },
            { name: 'Trust Wallet', key: 'trustWallet' },
            { name: 'Coin98', key: 'coin98' },
            { name: 'Glow', key: 'glow' },
            { name: 'MathWallet', key: 'mathWallet' }
        ];
        this.init();
    }

    init() {
        this.setupUI();
        this.checkPreviousConnection();
    }

    setupUI() {
        const walletButton = document.getElementById('wallet-connect-btn');
        const walletModal = document.getElementById('wallet-modal');
        const closeModal = document.getElementById('close-wallet-modal');
        const walletList = document.getElementById('wallet-list');

        if (walletButton) {
            walletButton.addEventListener('click', () => {
                if (this.connected) {
                    this.disconnect();
                } else {
                    this.showWalletModal();
                }
            });
        }

        if (closeModal) {
            closeModal.addEventListener('click', () => {
                this.hideWalletModal();
            });
        }

        if (walletModal) {
            walletModal.addEventListener('click', (e) => {
                if (e.target === walletModal) {
                    this.hideWalletModal();
                }
            });
        }

        this.populateWalletList();
    }

    populateWalletList() {
        const walletList = document.getElementById('wallet-list');
        if (!walletList) return;

        walletList.innerHTML = '';
        
        this.supportedWallets.forEach(wallet => {
            const isInstalled = this.isWalletInstalled(wallet.key);
            const walletItem = document.createElement('div');
            walletItem.className = `wallet-item ${!isInstalled ? 'wallet-not-installed' : ''}`;
            walletItem.innerHTML = `
                <div class="wallet-info">
                    <span class="wallet-name">${wallet.name}</span>
                    <span class="wallet-status">${isInstalled ? 'Installed' : 'Not Installed'}</span>
                </div>
            `;

            if (isInstalled) {
                walletItem.style.cursor = 'pointer';
                walletItem.addEventListener('click', () => {
                    this.connectWallet(wallet.key);
                });
            } else {
                walletItem.addEventListener('click', () => {
                    this.redirectToWallet(wallet.key);
                });
            }

            walletList.appendChild(walletItem);
        });
    }

    isWalletInstalled(walletKey) {
        switch (walletKey) {
            case 'phantom':
                return window.solana && window.solana.isPhantom;
            case 'solflare':
                return window.solflare && window.solflare.isSolflare;
            case 'slope':
                return window.Slope;
            case 'coinbaseWallet':
                return window.solana && window.solana.isCoinbaseWallet;
            case 'trustWallet':
                return window.solana && window.solana.isTrustWallet;
            case 'coin98':
                return window.coin98 && window.coin98.sol;
            case 'glow':
                return window.glowSolana;
            case 'mathWallet':
                return window.solana && window.solana.isMathWallet;
            default:
                return false;
        }
    }

    getWalletObject(walletKey) {
        switch (walletKey) {
            case 'phantom':
                return window.solana;
            case 'solflare':
                return window.solflare;
            case 'slope':
                return window.Slope;
            case 'coinbaseWallet':
                return window.solana;
            case 'trustWallet':
                return window.solana;
            case 'coin98':
                return window.coin98.sol;
            case 'glow':
                return window.glowSolana;
            case 'mathWallet':
                return window.solana;
            default:
                return null;
        }
    }

    async connectWallet(walletKey) {
        try {
            const walletObj = this.getWalletObject(walletKey);
            if (!walletObj) {
                throw new Error('Wallet not found');
            }

            const response = await walletObj.connect();
            this.wallet = walletObj;
            this.publicKey = response.publicKey.toString();
            this.connected = true;

            this.saveConnection(walletKey);
            this.updateUI();
            this.hideWalletModal();

            console.log('Connected to wallet:', this.publicKey);
        } catch (error) {
            console.error('Failed to connect wallet:', error);
            alert('Failed to connect wallet. Please try again.');
        }
    }

    async disconnect() {
        try {
            if (this.wallet && this.wallet.disconnect) {
                await this.wallet.disconnect();
            }
            
            this.wallet = null;
            this.publicKey = null;
            this.connected = false;
            
            this.clearConnection();
            this.updateUI();

            console.log('Disconnected from wallet');
        } catch (error) {
            console.error('Failed to disconnect wallet:', error);
        }
    }

    showWalletModal() {
        const modal = document.getElementById('wallet-modal');
        if (modal) {
            modal.style.display = 'flex';
            this.populateWalletList();
        }
    }

    hideWalletModal() {
        const modal = document.getElementById('wallet-modal');
        if (modal) {
            modal.style.display = 'none';
        }
    }

    updateUI() {
        const button = document.getElementById('wallet-connect-btn');
        if (!button) return;

        if (this.connected) {
            const shortAddress = `${this.publicKey.slice(0, 4)}...${this.publicKey.slice(-4)}`;
            button.innerHTML = `<i class="fas fa-wallet"></i> ${shortAddress}`;
            button.classList.add('connected');
        } else {
            button.innerHTML = '<i class="fas fa-wallet"></i> Connect Wallet';
            button.classList.remove('connected');
        }
    }

    saveConnection(walletKey) {
        localStorage.setItem('solana_wallet_connected', 'true');
        localStorage.setItem('solana_wallet_type', walletKey);
        localStorage.setItem('solana_wallet_address', this.publicKey);
    }

    clearConnection() {
        localStorage.removeItem('solana_wallet_connected');
        localStorage.removeItem('solana_wallet_type');
        localStorage.removeItem('solana_wallet_address');
    }

    checkPreviousConnection() {
        const wasConnected = localStorage.getItem('solana_wallet_connected');
        const walletType = localStorage.getItem('solana_wallet_type');
        const savedAddress = localStorage.getItem('solana_wallet_address');

        if (wasConnected && walletType && savedAddress) {
            if (this.isWalletInstalled(walletType)) {
                this.autoReconnect(walletType);
            }
        }
    }

    async autoReconnect(walletKey) {
        try {
            const walletObj = this.getWalletObject(walletKey);
            if (!walletObj) return;

            // Try to connect silently (only if wallet allows it)
            if (walletObj.isConnected) {
                this.wallet = walletObj;
                this.publicKey = walletObj.publicKey.toString();
                this.connected = true;
                this.updateUI();
            }
        } catch (error) {
            // Silent failure for auto-reconnect
            this.clearConnection();
        }
    }

    redirectToWallet(walletKey) {
        const walletUrls = {
            phantom: 'https://phantom.app',
            solflare: 'https://solflare.com',
            slope: 'https://slope.finance',
            coinbaseWallet: 'https://www.coinbase.com/wallet',
            trustWallet: 'https://trustwallet.com',
            coin98: 'https://coin98.com',
            glow: 'https://glow.app',
            mathWallet: 'https://mathwallet.org'
        };

        const url = walletUrls[walletKey];
        if (url) {
            window.open(url, '_blank');
        }
    }
}

// Initialize wallet adapter when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.solanaWalletAdapter = new SolanaWalletAdapter();
});