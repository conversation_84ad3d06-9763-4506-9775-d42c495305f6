/**
 * 简化的Solana钱包连接器
 * 直接使用钱包原生API，适用于静态HTML网站
 */
class DirectWalletConnector {
    constructor() {
        this.wallet = null;
        this.publicKey = null;
        this.connected = false;
        this.walletType = null;
        
        // 支持的主流钱包
        this.supportedWallets = [
            {
                name: 'Phantom',
                key: 'phantom',
                icon: '👻',
                detector: () => window.solana?.isPhantom,
                provider: () => window.solana,
                downloadUrl: 'https://phantom.app'
            },
            {
                name: 'Solflare',
                key: 'solflare',
                icon: '🔥',
                detector: () => window.solflare?.isSolflare,
                provider: () => window.solflare,
                downloadUrl: 'https://solflare.com'
            },
            {
                name: 'Backpack',
                key: 'backpack',
                icon: '🎒',
                detector: () => window.backpack,
                provider: () => window.backpack,
                downloadUrl: 'https://backpack.app'
            }
        ];

        this.init();
    }

    init() {
        this.setupUI();
        // 延迟检测钱包，给钱包注入时间
        setTimeout(() => {
            this.detectWallets();
            this.checkPreviousConnection();
        }, 500);
    }

    detectWallets() {
        console.log('🔍 开始检测钱包...');
        
        this.supportedWallets.forEach(wallet => {
            const isInstalled = wallet.detector();
            console.log(`${wallet.name}: ${isInstalled ? '✅ 已安装' : '❌ 未安装'}`);
        });
    }

    setupUI() {
        const connectBtn = document.getElementById('wallet-connect-btn');
        const modal = document.getElementById('wallet-modal');
        const closeBtn = document.getElementById('close-wallet-modal');

        if (connectBtn) {
            connectBtn.addEventListener('click', () => {
                if (this.connected) {
                    this.disconnect();
                } else {
                    this.showWalletModal();
                }
            });
        }

        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                this.hideWalletModal();
            });
        }

        if (modal) {
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    this.hideWalletModal();
                }
            });
        }

        this.populateWalletList();
    }

    populateWalletList() {
        const walletList = document.getElementById('wallet-list');
        if (!walletList) return;

        walletList.innerHTML = '';

        this.supportedWallets.forEach(wallet => {
            const isInstalled = wallet.detector();
            const walletItem = document.createElement('div');
            walletItem.className = `wallet-item ${!isInstalled ? 'wallet-not-installed' : ''}`;
            
            walletItem.innerHTML = `
                <div class="wallet-info">
                    <span class="wallet-name">${wallet.icon} ${wallet.name}</span>
                    <span class="wallet-status">${isInstalled ? '✅ 已安装' : '❌ 未安装'}</span>
                </div>
            `;

            if (isInstalled) {
                walletItem.style.cursor = 'pointer';
                walletItem.addEventListener('click', () => {
                    this.connectWallet(wallet.key);
                });
            } else {
                walletItem.style.cursor = 'pointer';
                walletItem.addEventListener('click', () => {
                    window.open(wallet.downloadUrl, '_blank');
                });
            }

            walletList.appendChild(walletItem);
        });
    }

    async connectWallet(walletKey) {
        try {
            const walletConfig = this.supportedWallets.find(w => w.key === walletKey);
            if (!walletConfig) {
                throw new Error('不支持的钱包类型');
            }

            const provider = walletConfig.provider();
            if (!provider) {
                throw new Error(`${walletConfig.name} 钱包未安装或不可用`);
            }

            console.log(`🔗 正在连接 ${walletConfig.name}...`);
            
            // 请求连接
            const response = await provider.connect();
            
            this.wallet = provider;
            this.walletType = walletKey;
            this.publicKey = response.publicKey.toString();
            this.connected = true;

            // 保存连接状态
            this.saveConnection(walletKey);
            
            // 设置事件监听
            this.setupWalletEvents();
            
            this.updateUI();
            this.hideWalletModal();

            console.log(`✅ 成功连接到 ${walletConfig.name}:`, this.publicKey);
            this.dispatchEvent('walletConnected', { 
                walletType: walletKey,
                publicKey: this.publicKey 
            });

        } catch (error) {
            console.error(`❌ 连接钱包失败:`, error);
            alert(`连接钱包失败: ${error.message}`);
        }
    }

    async disconnect() {
        try {
            if (this.wallet && this.wallet.disconnect) {
                await this.wallet.disconnect();
            }
            
            this.wallet = null;
            this.publicKey = null;
            this.connected = false;
            this.walletType = null;
            
            this.clearConnection();
            this.updateUI();

            console.log('✅ 已断开钱包连接');
            this.dispatchEvent('walletDisconnected');

        } catch (error) {
            console.error('❌ 断开连接失败:', error);
        }
    }

    setupWalletEvents() {
        if (!this.wallet) return;

        // 监听连接状态变化
        if (this.wallet.on) {
            this.wallet.on('connect', () => {
                console.log('🔗 钱包连接事件');
                this.connected = true;
                this.updateUI();
            });

            this.wallet.on('disconnect', () => {
                console.log('🔌 钱包断开事件');
                this.connected = false;
                this.publicKey = null;
                this.walletType = null;
                this.clearConnection();
                this.updateUI();
            });

            this.wallet.on('accountChanged', (publicKey) => {
                console.log('👤 账户变更:', publicKey?.toString());
                if (publicKey) {
                    this.publicKey = publicKey.toString();
                    this.updateUI();
                } else {
                    this.disconnect();
                }
            });
        }
    }

    showWalletModal() {
        const modal = document.getElementById('wallet-modal');
        if (modal) {
            modal.style.display = 'flex';
            this.populateWalletList();
        }
    }

    hideWalletModal() {
        const modal = document.getElementById('wallet-modal');
        if (modal) {
            modal.style.display = 'none';
        }
    }

    updateUI() {
        const connectBtn = document.getElementById('wallet-connect-btn');
        if (!connectBtn) return;

        if (this.connected) {
            const shortAddress = `${this.publicKey.slice(0, 4)}...${this.publicKey.slice(-4)}`;
            connectBtn.innerHTML = `<i class="fas fa-wallet"></i> ${shortAddress}`;
            connectBtn.classList.add('connected');
        } else {
            connectBtn.innerHTML = '<i class="fas fa-wallet"></i> Connect Wallet';
            connectBtn.classList.remove('connected');
        }
    }

    saveConnection(walletKey) {
        localStorage.setItem('solana_wallet_connected', 'true');
        localStorage.setItem('solana_wallet_type', walletKey);
        localStorage.setItem('solana_wallet_address', this.publicKey);
    }

    clearConnection() {
        localStorage.removeItem('solana_wallet_connected');
        localStorage.removeItem('solana_wallet_type');
        localStorage.removeItem('solana_wallet_address');
    }

    checkPreviousConnection() {
        const wasConnected = localStorage.getItem('solana_wallet_connected');
        const walletType = localStorage.getItem('solana_wallet_type');
        const savedAddress = localStorage.getItem('solana_wallet_address');

        if (wasConnected === 'true' && walletType && savedAddress) {
            console.log(`🔄 尝试自动重连 ${walletType} 钱包...`);
            this.autoReconnect(walletType);
        }
    }

    async autoReconnect(walletKey) {
        try {
            const walletConfig = this.supportedWallets.find(w => w.key === walletKey);
            if (!walletConfig) return;

            const provider = walletConfig.provider();
            if (!provider) return;

            // 检查是否已连接
            if (provider.isConnected && provider.publicKey) {
                this.wallet = provider;
                this.walletType = walletKey;
                this.publicKey = provider.publicKey.toString();
                this.connected = true;

                this.setupWalletEvents();
                this.updateUI();

                console.log(`✅ 自动重连成功 ${walletConfig.name}:`, this.publicKey);
            }
        } catch (error) {
            console.log('自动重连失败，清除缓存');
            this.clearConnection();
        }
    }

    // 获取钱包余额
    async getBalance() {
        if (!this.connected) return null;

        try {
            // 使用Solana Web3.js获取余额
            if (typeof solanaWeb3 !== 'undefined') {
                const connection = new solanaWeb3.Connection(
                    'https://api.mainnet-beta.solana.com',
                    'confirmed'
                );
                
                const balance = await connection.getBalance(
                    new solanaWeb3.PublicKey(this.publicKey)
                );
                
                return balance / solanaWeb3.LAMPORTS_PER_SOL;
            }
            
            return null;
        } catch (error) {
            console.error('获取余额失败:', error);
            return null;
        }
    }

    // 分发自定义事件
    dispatchEvent(eventName, data = {}) {
        const event = new CustomEvent(eventName, { detail: data });
        document.dispatchEvent(event);
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    window.directWalletConnector = new DirectWalletConnector();
    
    // 监听钱包连接事件
    document.addEventListener('walletConnected', (event) => {
        console.log('💼 钱包连接成功:', event.detail);
    });
    
    document.addEventListener('walletDisconnected', () => {
        console.log('💼 钱包已断开');
    });
});