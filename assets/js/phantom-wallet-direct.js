/**
 * Direct Phantom Wallet Integration for Vanilla JavaScript
 * 使用window.solana直接集成Phantom钱包
 */

class PhantomWalletAdapter {
    constructor() {
        this.wallet = null;
        this.publicKey = null;
        this.connected = false;
        this.init();
    }

    init() {
        this.setupUI();
        this.checkWalletAvailability();
        this.setupEventListeners();
    }

    // 检查Phantom钱包是否可用
    checkWalletAvailability() {
        if (window.solana && window.solana.isPhantom) {
            console.log('Phantom wallet detected');
            this.wallet = window.solana;
            this.checkAutoConnect();
        } else {
            console.log('Phantom wallet not detected');
            this.showInstallPrompt();
        }
    }

    // 自动连接之前连接过的钱包
    async checkAutoConnect() {
        if (this.wallet.isConnected) {
            this.publicKey = this.wallet.publicKey.toString();
            this.connected = true;
            this.updateUI();
            console.log('Auto-connected to Phantom:', this.publicKey);
        }
    }

    // 连接钱包
    async connectWallet() {
        try {
            if (!this.wallet) {
                throw new Error('Phantom wallet not installed');
            }

            // 请求连接
            const response = await this.wallet.connect();
            this.publicKey = response.publicKey.toString();
            this.connected = true;
            
            // 保存连接状态
            localStorage.setItem('phantom_connected', 'true');
            
            this.updateUI();
            console.log('Connected to Phantom:', this.publicKey);
            
            // 触发自定义事件
            this.dispatchEvent('walletConnected', { publicKey: this.publicKey });
            
        } catch (error) {
            console.error('Failed to connect to Phantom:', error);
            alert('连接钱包失败: ' + error.message);
        }
    }

    // 断开连接
    async disconnectWallet() {
        try {
            if (this.wallet && this.connected) {
                await this.wallet.disconnect();
            }
            
            this.publicKey = null;
            this.connected = false;
            
            // 清除连接状态
            localStorage.removeItem('phantom_connected');
            
            this.updateUI();
            console.log('Disconnected from Phantom');
            
            // 触发自定义事件
            this.dispatchEvent('walletDisconnected');
            
        } catch (error) {
            console.error('Failed to disconnect from Phantom:', error);
        }
    }

    // 获取余额
    async getBalance() {
        if (!this.connected) return null;
        
        try {
            // 连接到Solana网络
            const connection = new solanaWeb3.Connection(
                'https://api.mainnet-beta.solana.com'
            );
            
            const balance = await connection.getBalance(
                new solanaWeb3.PublicKey(this.publicKey)
            );
            
            // 转换为SOL (1 SOL = 1,000,000,000 lamports)
            return balance / 1000000000;
            
        } catch (error) {
            console.error('Failed to get balance:', error);
            return null;
        }
    }

    // 发送交易示例
    async sendTransaction(toAddress, amount) {
        if (!this.connected) {
            throw new Error('Wallet not connected');
        }

        try {
            // 连接到Solana网络
            const connection = new solanaWeb3.Connection(
                'https://api.mainnet-beta.solana.com'
            );

            // 创建交易
            const transaction = new solanaWeb3.Transaction().add(
                solanaWeb3.SystemProgram.transfer({
                    fromPubkey: new solanaWeb3.PublicKey(this.publicKey),
                    toPubkey: new solanaWeb3.PublicKey(toAddress),
                    lamports: amount * 1000000000 // 转换为lamports
                })
            );

            // 获取最新区块哈希
            const { blockhash } = await connection.getRecentBlockhash();
            transaction.recentBlockhash = blockhash;
            transaction.feePayer = new solanaWeb3.PublicKey(this.publicKey);

            // 通过Phantom签名并发送
            const signedTransaction = await this.wallet.signTransaction(transaction);
            const signature = await connection.sendRawTransaction(signedTransaction.serialize());
            
            // 确认交易
            await connection.confirmTransaction(signature);
            
            console.log('Transaction sent:', signature);
            return signature;
            
        } catch (error) {
            console.error('Failed to send transaction:', error);
            throw error;
        }
    }

    // 设置UI事件监听器
    setupUI() {
        const connectBtn = document.getElementById('phantom-connect-btn');
        if (connectBtn) {
            connectBtn.addEventListener('click', () => {
                if (this.connected) {
                    this.disconnectWallet();
                } else {
                    this.connectWallet();
                }
            });
        }

        const getBalanceBtn = document.getElementById('get-balance-btn');
        if (getBalanceBtn) {
            getBalanceBtn.addEventListener('click', async () => {
                const balance = await this.getBalance();
                const balanceDisplay = document.getElementById('balance-display');
                if (balanceDisplay) {
                    balanceDisplay.textContent = balance !== null ? 
                        `${balance.toFixed(4)} SOL` : 'Failed to get balance';
                }
            });
        }
    }

    // 监听钱包事件
    setupEventListeners() {
        if (this.wallet) {
            // 监听账户变化
            this.wallet.on('accountChanged', (publicKey) => {
                if (publicKey) {
                    this.publicKey = publicKey.toString();
                    this.connected = true;
                    console.log('Account changed:', this.publicKey);
                } else {
                    this.publicKey = null;
                    this.connected = false;
                    console.log('Account disconnected');
                }
                this.updateUI();
            });

            // 监听连接状态变化
            this.wallet.on('connect', (publicKey) => {
                this.publicKey = publicKey.toString();
                this.connected = true;
                console.log('Wallet connected:', this.publicKey);
                this.updateUI();
            });

            // 监听断开连接
            this.wallet.on('disconnect', () => {
                this.publicKey = null;
                this.connected = false;
                console.log('Wallet disconnected');
                this.updateUI();
            });
        }
    }

    // 更新UI
    updateUI() {
        const connectBtn = document.getElementById('phantom-connect-btn');
        const statusDisplay = document.getElementById('wallet-status');
        const addressDisplay = document.getElementById('wallet-address');

        if (connectBtn) {
            if (this.connected) {
                connectBtn.textContent = 'Disconnect Phantom';
                connectBtn.className = 'wallet-btn connected';
            } else {
                connectBtn.textContent = 'Connect Phantom';
                connectBtn.className = 'wallet-btn';
            }
        }

        if (statusDisplay) {
            statusDisplay.textContent = this.connected ? 'Connected' : 'Disconnected';
            statusDisplay.className = this.connected ? 'status connected' : 'status disconnected';
        }

        if (addressDisplay) {
            addressDisplay.textContent = this.connected ? 
                `${this.publicKey.slice(0, 4)}...${this.publicKey.slice(-4)}` : '';
        }
    }

    // 显示安装提示
    showInstallPrompt() {
        const installPrompt = document.getElementById('install-prompt');
        if (installPrompt) {
            installPrompt.style.display = 'block';
        }
    }

    // 分发自定义事件
    dispatchEvent(eventName, data = {}) {
        const event = new CustomEvent(eventName, { detail: data });
        document.dispatchEvent(event);
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    window.phantomWallet = new PhantomWalletAdapter();
    
    // 监听钱包连接事件
    document.addEventListener('walletConnected', (event) => {
        console.log('Wallet connected event:', event.detail);
        // 在这里添加连接成功后的逻辑
    });
    
    // 监听钱包断开事件
    document.addEventListener('walletDisconnected', () => {
        console.log('Wallet disconnected event');
        // 在这里添加断开连接后的逻辑
    });
});

// 导出到全局作用域
window.PhantomWalletAdapter = PhantomWalletAdapter;