#!/usr/bin/env python3
"""
简单的HTTP服务器，用于测试钱包连接
使用方法：python3 start-server.py
然后访问：http://localhost:8000
"""

import http.server
import socketserver
import os
import webbrowser
from pathlib import Path

# 设置端口
PORT = 8000

# 确保在正确的目录
os.chdir(Path(__file__).parent)

class MyHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def end_headers(self):
        # 添加CORS头，允许钱包扩展访问
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', '*')
        super().end_headers()

    def log_message(self, format, *args):
        # 简化日志输出
        print(f"[{self.address_string()}] {format % args}")

def main():
    try:
        with socketserver.TCPServer(("", PORT), MyHTTPRequestHandler) as httpd:
            print(f"🚀 服务器启动成功!")
            print(f"📍 本地地址: http://localhost:{PORT}")
            print(f"📁 服务目录: {os.getcwd()}")
            print(f"🔗 测试页面:")
            print(f"   - 主页: http://localhost:{PORT}/index.html")
            print(f"   - 调试: http://localhost:{PORT}/debug-wallet.html")
            print(f"   - 简单测试: http://localhost:{PORT}/wallet-simple-test.html")
            print(f"\n💡 提示: 使用 Ctrl+C 停止服务器")
            print(f"🌐 正在自动打开浏览器...")
            
            # 自动打开浏览器
            webbrowser.open(f'http://localhost:{PORT}/debug-wallet.html')
            
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print(f"\n👋 服务器已停止")
    except OSError as e:
        if e.errno == 48:  # Address already in use
            print(f"❌ 端口 {PORT} 已被占用")
            print(f"💡 请尝试关闭其他服务或使用不同端口")
        else:
            print(f"❌ 启动服务器失败: {e}")

if __name__ == "__main__":
    main()
